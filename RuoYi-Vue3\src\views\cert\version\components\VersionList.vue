<template>
  <div class="version-list">
    <!-- 顶部工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button type="primary" @click="handleCreateVersion">
          <el-icon><Plus /></el-icon>
          [+ 新建版本]
        </el-button>
      </div>
      
      <div class="toolbar-right">
        <!-- 搜索框 -->
        <el-input
          v-model="searchKeyword"
          placeholder="搜索版本代码或描述"
          clearable
          style="width: 250px; margin-right: 12px"
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        
        <!-- 状态筛选 -->
        <el-select
          v-model="statusFilter"
          placeholder="状态筛选"
          clearable
          style="width: 150px; margin-right: 12px"
          @change="handleFilterChange"
        >
          <el-option label="全部" value="" />
          <el-option label="待设定标准" value="awaiting_standard" />
          <el-option label="待标注" value="awaiting_annotation" />
          <el-option label="已完成" value="completed" />
        </el-select>
        
        <!-- 排序选择 -->
        <el-select
          v-model="sortOrder"
          placeholder="排序方式"
          style="width: 180px"
          @change="handleSortChange"
        >
          <el-option label="按创建时间 (新→旧)" value="createTime_desc" />
          <el-option label="按创建时间 (旧→新)" value="createTime_asc" />
        </el-select>
      </div>
    </div>

    <!-- 版本列表表格 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="filteredVersions"
        stripe
        border
        style="width: 100%"
        height="calc(100vh - 300px)"
      >
        <!-- 版本代码列 -->
        <el-table-column
          prop="versionCode"
          label="版本代码"
          min-width="180"
          show-overflow-tooltip
        >
          <template #default="scope">
            <span class="version-code">{{ scope.row.versionCode }}</span>
          </template>
        </el-table-column>
        
        <!-- 包含文件夹数列 -->
        <el-table-column
          prop="folderCount"
          label="包含文件夹数"
          width="120"
          align="center"
        >
          <template #default="scope">
            <el-tag type="info">{{ scope.row.folderCount || 0 }}</el-tag>
          </template>
        </el-table-column>
        
        <!-- 状态列 -->
        <el-table-column
          prop="status"
          label="状态"
          width="120"
          align="center"
        >
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <!-- 创建时间列 -->
        <el-table-column
          prop="createTime"
          label="创建时间"
          width="160"
          align="center"
        >
          <template #default="scope">
            {{ formatTime(scope.row.createTime) }}
          </template>
        </el-table-column>
        
        <!-- 操作列 -->
        <el-table-column
          label="操作"
          width="120"
          align="center"
          fixed="right"
        >
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              @click="handleEnterVersion(scope.row)"
            >
              进入版本
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Plus, Search } from '@element-plus/icons-vue'
import { getVersionList, getVersionsByTaskId, type CertVersionVO } from '@/api/samples/version'

// 定义Props
interface Props {
  taskId: string
}

const props = defineProps<Props>()

// 定义Emits
const emit = defineEmits<{
  'create-version-clicked': []
}>()

// 响应式数据
const loading = ref(false)
const versions = ref<CertVersionVO[]>([])
const searchKeyword = ref('')
const statusFilter = ref('')
const sortOrder = ref('createTime_desc')

// 路由
const router = useRouter()

// 计算属性：筛选和排序后的版本列表
const filteredVersions = computed(() => {
  let result = [...versions.value]
  
  // 关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(version => 
      version.versionCode?.toLowerCase().includes(keyword) ||
      version.description?.toLowerCase().includes(keyword)
    )
  }
  
  // 状态筛选
  if (statusFilter.value) {
    result = result.filter(version => version.status === statusFilter.value)
  }
  
  // 排序
  result.sort((a, b) => {
    const timeA = new Date(a.createTime).getTime()
    const timeB = new Date(b.createTime).getTime()
    
    if (sortOrder.value === 'createTime_desc') {
      return timeB - timeA
    } else {
      return timeA - timeB
    }
  })
  
  return result
})

// 获取版本列表
const loadVersions = async () => {
  loading.value = true
  try {
    // TODO: 需要添加新的API接口 getVersionsByTaskId(taskId)
    // 目前使用临时的解决方案，通过关键词搜索来模拟按任务ID查询
    const response = await getVersionsByTaskId({
      keyword: props.taskId, // 临时使用taskId作为关键词
      pageSize: 10 // 获取足够多的数据
    })
    
    if (response.code === 200) {
      versions.value = response.data?.rows || response.rows || []
      
      // 临时：过滤出包含taskId的版本（实际应该通过API直接获取）
      // 这里假设版本数据中有taskId字段，或者通过其他方式关联
      console.log('获取到版本列表:', versions.value)
    } else {
      ElMessage.error(response.msg || '加载版本列表失败')
    }
  } catch (error) {
    console.error('加载版本列表失败:', error)
    ElMessage.error('加载版本列表失败')
  } finally {
    loading.value = false
  }
}

// 处理搜索
const handleSearch = () => {
  // 实时搜索，不需要额外处理，computed会自动更新
}

// 处理筛选变化
const handleFilterChange = () => {
  // 实时筛选，不需要额外处理，computed会自动更新
}

// 处理排序变化
const handleSortChange = () => {
  // 实时排序，不需要额外处理，computed会自动更新
}

// 处理新建版本
const handleCreateVersion = () => {
  emit('create-version-clicked')
}

// 处理进入版本
const handleEnterVersion = (version: CertVersionVO) => {
  router.push(`/version-detail/${version.versionId}`)
}

// 获取状态类型
const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    'awaiting_standard': 'warning',
    'awaiting_annotation': 'primary',
    'completed': 'success'
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'awaiting_standard': '待设定标准',
    'awaiting_annotation': '待标注',
    'completed': '已完成'
  }
  return statusMap[status] || '未知'
}

// 格式化时间
const formatTime = (time: string) => {
  if (!time) return '-'
  return new Date(time).toLocaleString('zh-CN')
}

// 监听taskId变化
watch(() => props.taskId, (newTaskId) => {
  if (newTaskId) {
    loadVersions()
  }
}, { immediate: true })

// 生命周期
onMounted(() => {
  if (props.taskId) {
    loadVersions()
  }
})
</script>

<style scoped>
.version-list {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #ebeef5;
  margin-bottom: 16px;
}

.toolbar-left {
  display: flex;
  align-items: center;
}

.toolbar-right {
  display: flex;
  align-items: center;
}

.table-container {
  flex: 1;
  overflow: hidden;
}

.version-code {
  font-family: 'Courier New', monospace;
  font-weight: 500;
  color: #409eff;
}

:deep(.el-table) {
  border-radius: 8px;
}

:deep(.el-table th) {
  background-color: #fafafa;
  font-weight: 600;
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background-color: #fafafa;
}
</style> 