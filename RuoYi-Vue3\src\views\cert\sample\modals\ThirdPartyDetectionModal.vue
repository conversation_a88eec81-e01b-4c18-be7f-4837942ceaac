<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`第三方版本检测 - ${props.targetFolder?.folderName || ''}`"
    width="900px"
    :before-close="handleClose"
    :close-on-click-modal="false"
  >
    <!-- 检测进度区域 -->
    <div v-if="detecting" class="detection-progress">
      <div class="progress-header">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span>正在进行第三方版本检测...</span>
      </div>
      <el-progress
        :percentage="progressPercentage"
        :status="progressStatus"
        :stroke-width="8"
        class="progress-bar"
      />
      <div class="progress-text">{{ progressText }}</div>
    </div>

    <!-- 检测结果展示区域 -->
    <div v-else-if="detectionResult" class="detection-result">
      <!-- 检测状态信息 -->
      <div class="result-header">
        <el-tag
          :type="detectionResult.detectionStatus === 'success' ? 'success' :
                 detectionResult.detectionStatus === 'no_match' ? 'warning' : 'danger'"
          size="large"
        >
          {{ getStatusText(detectionResult.detectionStatus) }}
        </el-tag>
        <span class="detection-time">
          检测时间: {{ formatTime(detectionResult.detectionTime) }}
        </span>
        <span v-if="detectionResult.confidence" class="confidence">
          置信度: {{ (detectionResult.confidence * 100).toFixed(1) }}%
        </span>
      </div>

      <!-- 匹配版本列表 -->
      <div v-if="detectionResult.matchedVersions && detectionResult.matchedVersions.length > 0" class="matched-versions">
        <h4>检测到的匹配版本</h4>
        <el-table
          :data="detectionResult.matchedVersions"
          @row-click="handleRowClick"
          highlight-current-row
          style="width: 100%; margin-top: 12px"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="versionCode" label="版本代码" min-width="150" />
          <el-table-column label="匹配度" min-width="100">
            <template #default="{ row }">
              <el-progress
                :percentage="(row.matchScore * 100)"
                :color="getScoreColor(row.matchScore)"
                :stroke-width="6"
                :show-text="false"
              />
              <span class="score-text">{{ (row.matchScore * 100).toFixed(1) }}%</span>
            </template>
          </el-table-column>
          <el-table-column prop="matchReason" label="匹配原因" min-width="200" show-overflow-tooltip />
          <el-table-column label="操作" min-width="120">
            <template #default="{ row }">
              <el-button size="small" type="primary" @click="selectVersion(row)">
                选择此版本
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 无匹配结果时的处理选项 -->
      <div v-else class="no-match-options">
        <el-empty description="未检测到匹配的版本">
          <template #image>
            <el-icon size="60"><Search /></el-icon>
          </template>
        </el-empty>
        <div class="options">
          <el-button type="primary" @click="handleManualProcess">转入手工处理</el-button>
          <el-button @click="startDetection">重新检测</el-button>
        </div>
      </div>

      <!-- 错误信息 -->
      <div v-if="detectionResult.errorMessage" class="error-message">
        <el-alert
          :title="detectionResult.errorMessage"
          type="error"
          :closable="false"
        />
      </div>
    </div>

    <!-- 初始状态 -->
    <div v-else class="initial-state">
      <el-empty description="点击开始检测按钮进行第三方版本检测">
        <template #image>
          <el-icon size="60"><Cpu /></el-icon>
        </template>
        <el-button type="primary" @click="startDetection">开始检测</el-button>
      </el-empty>
    </div>

    <!-- 操作按钮 -->
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button v-if="!detecting && !detectionResult" type="primary" @click="startDetection">
          开始检测
        </el-button>
        <el-button v-if="detectionResult && !detecting" @click="startDetection">
          重新检测
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Loading, Search, Cpu } from '@element-plus/icons-vue'
// 导入API
import { detectVersionByThirdParty } from '@/api/samples/folder'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  targetFolder: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'detection-success', 'version-selected', 'manual-process'])

// 响应式数据
const dialogVisible = ref(false)
const detecting = ref(false)
const detectionResult = ref(null)
const selectedVersion = ref(null)
const progressPercentage = ref(0)
const progressStatus = ref('')
const progressText = ref('')

// 监听 modelValue 变化
watch(() => props.modelValue, (newVal) => {
  dialogVisible.value = newVal
  if (newVal) {
    resetState()
  }
})

// 监听 dialogVisible 变化
watch(dialogVisible, (newVal) => {
  if (!newVal) {
    emit('update:modelValue', false)
  }
})

// 开始第三方检测
async function startDetection() {
  detecting.value = true
  detectionResult.value = null
  progressPercentage.value = 0
  progressStatus.value = ''
  progressText.value = '正在初始化检测...'

  try {
    // 模拟检测进度
    const progressSteps = [
      { percentage: 20, text: '正在分析文件夹内容...' },
      { percentage: 40, text: '正在提取特征信息...' },
      { percentage: 60, text: '正在调用第三方API...' },
      { percentage: 80, text: '正在分析检测结果...' },
      { percentage: 100, text: '检测完成' }
    ]

    for (const step of progressSteps) {
      await new Promise(resolve => setTimeout(resolve, 800))
      progressPercentage.value = step.percentage
      progressText.value = step.text
    }

    // 调用实际的检测API
    const response = await detectVersionByThirdParty(props.targetFolder.folderId)
    if (response.code === 200) {
      detectionResult.value = response.data
      progressStatus.value = 'success'
      ElMessage.success('检测完成')
      emit('detection-success')
    } else {
      throw new Error(response.msg || '检测失败')
    }
  } catch (error) {
    console.error('版本检测失败:', error)
    progressStatus.value = 'exception'
    progressText.value = '检测失败: ' + error.message
    ElMessage.error('版本检测失败: ' + error.message)
  } finally {
    detecting.value = false
  }
}

// 选择版本
function selectVersion(version) {
  selectedVersion.value = version
  emit('version-selected', version)
  ElMessage.success(`已选择版本: ${version.versionCode}`)
  handleClose()
}

// 转入手工处理
function handleManualProcess() {
  emit('manual-process')
  ElMessage.info('已转入手工处理流程')
  handleClose()
}

// 处理行点击
function handleRowClick(row) {
  selectedVersion.value = row
}

// 关闭弹窗
function handleClose() {
  dialogVisible.value = false
}

// 重置状态
function resetState() {
  detecting.value = false
  detectionResult.value = null
  selectedVersion.value = null
  progressPercentage.value = 0
  progressStatus.value = ''
  progressText.value = ''
}

// 工具函数
function getStatusText(status) {
  const statusMap = {
    'success': '检测成功',
    'no_match': '未找到匹配',
    'failed': '检测失败'
  }
  return statusMap[status] || '未知状态'
}

function formatTime(time) {
  if (!time) return '-'
  return new Date(time).toLocaleString()
}

function getScoreColor(score) {
  if (score >= 0.8) return '#67c23a'
  if (score >= 0.6) return '#e6a23c'
  return '#f56c6c'
}
</script>

<style scoped lang="scss">
.detection-progress {
  text-align: center;
  padding: 40px 20px;

  .progress-header {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    font-size: 16px;
    color: #409eff;

    .el-icon {
      margin-right: 8px;
      font-size: 18px;
    }
  }

  .progress-bar {
    margin-bottom: 16px;
  }

  .progress-text {
    color: #606266;
    font-size: 14px;
  }
}

.detection-result {
  .result-header {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 6px;

    .detection-time, .confidence {
      color: #606266;
      font-size: 14px;
    }
  }

  .matched-versions {
    h4 {
      margin: 0 0 12px 0;
      color: #303133;
    }

    .score-text {
      margin-left: 8px;
      font-size: 12px;
      color: #606266;
    }
  }

  .no-match-options {
    text-align: center;
    padding: 20px;

    .options {
      margin-top: 20px;
      display: flex;
      justify-content: center;
      gap: 12px;
    }
  }

  .error-message {
    margin-top: 16px;
  }
}

.initial-state {
  text-align: center;
  padding: 40px 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-table) {
  .el-table__row {
    cursor: pointer;
  }

  .el-table__row:hover {
    background-color: #f5f7fa;
  }
}

:deep(.el-progress) {
  .el-progress__text {
    font-size: 12px !important;
  }
}
</style>
