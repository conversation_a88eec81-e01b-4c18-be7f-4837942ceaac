<template>
  <el-dialog
    v-model="dialogVisible"
    title="文件上传进度"
    width="900px"
    :modal="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="!isUploading"
    @close="handleClose"
  >
    <!-- Uppy Dashboard 挂载容器 -->
    <div id="uppy-dashboard-container" class="uppy-dashboard-wrapper"></div>

    <!-- 上传完成提示 -->
    <div v-if="uploadCompleted" class="upload-completion-section">
      <el-result
        icon="success"
        title="上传完成！"
        :sub-title="completionMessage"
      >
        <template #extra>
          <el-button type="primary" @click="handleClose">
            关闭窗口
          </el-button>
          <el-button @click="handleReset">
            重新上传
          </el-button>
        </template>
      </el-result>
    </div>

    <!-- 上传失败提示 -->
    <div v-if="uploadFailed" class="upload-failure-section">
      <el-result
        icon="error"
        title="上传失败"
        :sub-title="failureMessage"
      >
        <template #extra>
          <el-button type="primary" @click="handleRetry">
            重试上传
          </el-button>
          <el-button type="warning" @click="clearTusState">
            清理状态重传
          </el-button>
          <el-button type="info" @click="runNetworkDiagnostics">
            网络诊断
          </el-button>
          <el-button type="success" @click="testTusUpload">
            测试上传
          </el-button>
          <el-button @click="handleClose">
            关闭窗口
          </el-button>
        </template>
      </el-result>
    </div>

    <!-- 上传统计信息 -->
    <div v-if="showStats && !uploadCompleted && !uploadFailed" class="upload-stats">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-statistic title="总文件数" :value="uploadStats.totalFiles" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="已上传" :value="uploadStats.uploadedFiles" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="失败文件" :value="uploadStats.failedFiles" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="上传进度" :value="uploadStats.progress" suffix="%" />
        </el-col>
      </el-row>
    </div>

    <template #footer v-if="!uploadCompleted && !uploadFailed">
      <span class="dialog-footer">
        <el-button
          v-if="!isUploading"
          @click="handleClose"
        >
          取消
        </el-button>
        <el-button
          v-if="isUploading"
          type="warning"
          @click="handlePauseResume"
        >
          {{ isPaused ? '继续上传' : '暂停上传' }}
        </el-button>
        <el-button
          v-if="isUploading"
          type="danger"
          @click="handleCancelUpload"
        >
          取消上传
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import Dashboard from '@uppy/dashboard'
import { runConnectionDiagnostics, checkLoginStatus, redirectToLogin } from '@/utils/connectionTest.js'
import { testTusUpload as testTusUploadFunction } from '@/utils/uploadTest.js'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  uppy: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['close', 'upload-complete', 'upload-failed'])

// 响应式数据
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => {
    if (!value) {
      emit('close')
    }
  }
})

// 上传状态
const isUploading = ref(false)
const isPaused = ref(false)
const uploadCompleted = ref(false)
const uploadFailed = ref(false)
const showStats = ref(false)

// 消息文本
const completionMessage = ref('')
const failureMessage = ref('')

// 上传统计
const uploadStats = ref({
  totalFiles: 0,
  uploadedFiles: 0,
  failedFiles: 0,
  progress: 0
})

// Dashboard 实例引用
let dashboardInstance = null

/**
 * 安全挂载 Uppy Dashboard
 */
 const mountDashboard = async () => {
  if (!props.uppy) {
    console.error('Uppy 实例不存在，无法挂载 Dashboard')
    return
  }

  // 检查是否已经挂载
  const existingDashboard = props.uppy.getPlugin('Dashboard')
  if (existingDashboard) {
    console.log('Dashboard 已存在，跳过重复挂载')
    dashboardInstance = existingDashboard
    return
  }

  try {
    // 关键修复：确保所有文件都有有效的文件名
    const files = props.uppy.getFiles()
    console.log(`挂载前检查 ${files.length} 个文件的名称`)

    files.forEach((file, index) => {
      console.log(`检查文件 ${index + 1}: ID=${file.id}, name="${file.name}" (类型: ${typeof file.name})`)

      if (!file.name || typeof file.name !== 'string' || file.name.trim() === '') {
        console.warn(`检测到无效文件名，正在修复。ID: ${file.id}, 原始文件名: ${file.name}`)

        // 尝试从多个来源获取文件名
        const newName = file.meta?.fileName ||
                       file.meta?.originalFileName ||
                       file.meta?.name ||
                       (file.data && file.data.name) ||
                       `unnamed-file-${Date.now()}-${index}`

        console.log(`修复文件名为: "${newName}"`)

        // 更新文件状态
        props.uppy.setFileState(file.id, {
          name: newName,
          meta: {
            ...file.meta,
            fileName: newName,
            originalFileName: newName
          }
        })

        console.log(`✅ 文件名已修复: ${file.id} -> "${newName}"`)
      } else {
        console.log(`✅ 文件名正常: "${file.name}"`)
      }
    })

    // 再次验证所有文件名
    const updatedFiles = props.uppy.getFiles()
    const invalidFiles = updatedFiles.filter(file => !file.name || typeof file.name !== 'string' || file.name.trim() === '')

    if (invalidFiles.length > 0) {
      console.error('仍有文件名无效的文件:', invalidFiles.map(f => ({ id: f.id, name: f.name })))
      // 移除无效文件
      invalidFiles.forEach(file => {
        console.log(`移除无效文件: ${file.id}`)
        props.uppy.removeFile(file.id)
      })
    }
    // Dashboard 配置选项
    const dashboardOptions = {
      target: '#uppy-dashboard-container',
      inline: true,
      height: 400,
      width: '100%',
      showProgressDetails: true,
      proudlyDisplayPoweredByUppy: false,
      hideUploadButton: false,
      hideRetryButton: false,
      hidePauseResumeButton: false,
      hideCancelButton: false,
      showRemoveButtonAfterComplete: true,
      // 尝试禁用可能导致问题的功能
      disableStatusBar: false,
      disableInformer: false,
      disableThumbnailGenerator: true,
      // 简化本地化配置
      locale: {
        strings: {
          dropHereOr: '拖拽文件到这里或者 %{browse}',
          browse: '选择文件',
          // 添加更多本地化字符串以避免 undefined
          addMore: '添加更多文件',
          addMoreFiles: '添加更多文件',
          addingMoreFiles: '添加更多文件',
          uploadComplete: '上传完成',
          uploadPaused: '上传已暂停',
          resumeUpload: '继续上传',
          pauseUpload: '暂停上传',
          retryUpload: '重试上传',
          cancelUpload: '取消上传',
          xFilesSelected: {
            0: '未选择文件',
            1: '已选择 %{smart_count} 个文件',
            2: '已选择 %{smart_count} 个文件'
          },
          uploadingXFiles: {
            0: '未上传文件',
            1: '正在上传 %{smart_count} 个文件',
            2: '正在上传 %{smart_count} 个文件'
          }
        }
      }
    }

    console.log('正在挂载 Dashboard 插件...')

    // 尝试安全挂载 Dashboard
    try {
      // 先临时移除所有文件，避免渲染问题
      const tempFiles = props.uppy.getFiles()
      const fileBackups = tempFiles.map(file => ({
        id: file.id,
        name: file.name || 'unnamed-file',
        type: file.type,
        data: file.data,
        size: file.size,
        meta: file.meta
      }))

      // 清空文件列表
      tempFiles.forEach(file => props.uppy.removeFile(file.id))
      console.log('临时清空文件列表以安全挂载 Dashboard')

      // 挂载 Dashboard
      props.uppy.use(Dashboard, dashboardOptions)
      console.log('Dashboard 插件挂载完成')
      dashboardInstance = props.uppy.getPlugin('Dashboard')

      // 等待一个微任务周期，确保 Dashboard 完全初始化
      await new Promise(resolve => setTimeout(resolve, 0))

      // 重新添加文件
      fileBackups.forEach((fileBackup, index) => {
        try {
          const safeName = fileBackup.name || `file-${index + 1}`
          props.uppy.addFile({
            id: fileBackup.id,
            name: safeName,
            type: fileBackup.type,
            data: fileBackup.data,
            meta: {
              ...fileBackup.meta,
              fileName: safeName,
              originalFileName: safeName
            }
          })
        } catch (addError) {
          console.error(`重新添加文件失败: ${fileBackup.name}`, addError)
        }
      })

      console.log(`✅ Dashboard 挂载成功，已恢复 ${props.uppy.getFiles().length} 个文件`)

    } catch (dashboardError) {
      console.error('Dashboard 挂载过程中出错:', dashboardError)
      throw dashboardError
    }

    if (dashboardInstance) {
      console.log('Uppy Dashboard 已成功挂载')
      setupEventListeners()
    } else {
      console.error('Dashboard 挂载失败：无法获取插件实例')
    }

  } catch (error) {
    console.error('挂载 Dashboard 失败:', error)
    ElMessage.error('初始化上传界面失败')
    dashboardInstance = null
  }
}

/**
 * 安全卸载 Uppy Dashboard
 */
const unmountDashboard = () => {
  if (!props.uppy) {
    console.warn('Uppy 实例不存在，跳过卸载')
    dashboardInstance = null
    return
  }

  try {
    // 检查 Dashboard 插件是否存在
    const existingDashboard = props.uppy.getPlugin('Dashboard')
    if (existingDashboard) {
      console.log('正在卸载 Dashboard 插件...')
      props.uppy.removePlugin('Dashboard')
      console.log('Dashboard 插件已成功卸载')
    } else {
      console.log('Dashboard 插件不存在，无需卸载')
    }

    // 清理实例引用
    dashboardInstance = null

  } catch (error) {
    console.error('卸载 Dashboard 失败:', error)
    // 强制清理引用，防止内存泄漏
    dashboardInstance = null
  }
}

/**
 * 设置事件监听器
 */
const setupEventListeners = () => {
  if (!props.uppy) return

  // 上传开始
  props.uppy.on('upload', () => {
    console.log('开始上传...')
    isUploading.value = true
    isPaused.value = false
    uploadCompleted.value = false
    uploadFailed.value = false
    showStats.value = true

    // 重置统计数据
    uploadStats.value = {
      totalFiles: props.uppy.getFiles().length,
      uploadedFiles: 0,
      failedFiles: 0,
      progress: 0
    }
  })

  // 上传进度
  props.uppy.on('progress', (progress) => {
    uploadStats.value.progress = Math.round(progress)
  })

  // 单个文件上传成功
  props.uppy.on('upload-success', (file, response) => {
    uploadStats.value.uploadedFiles++
    console.log(`文件上传成功: ${file.name}`)
  })

  // 单个文件上传失败
  props.uppy.on('upload-error', (file, error) => {
    uploadStats.value.failedFiles++
    console.error(`文件上传失败: ${file.name}`, error)

    // 检查是否是网络连接错误
    if (error.message && (
      error.message.includes('network error') ||
      error.message.includes('Failed to fetch') ||
      error.message.includes('blocked by an internet provider') ||
      error.message.includes('endpoint might be blocked')
    )) {
      console.warn(`文件 ${file.name} 遇到网络连接错误`)

      ElMessage.error({
        message: `文件 ${file.name} 上传失败：网络连接异常。请检查网络连接和防火墙设置，或点击"网络诊断"按钮进行详细检查。`,
        duration: 10000,
        showClose: true
      })

      // 建议运行网络诊断
      setTimeout(() => {
        ElMessageBox.confirm(
          '检测到网络连接问题，是否运行网络诊断以找出具体原因？',
          '网络诊断建议',
          {
            confirmButtonText: '运行诊断',
            cancelButtonText: '稍后再说',
            type: 'warning'
          }
        ).then(() => {
          runNetworkDiagnostics()
        }).catch(() => {
          // 用户选择不运行诊断
        })
      }, 2000)

    } else if (error.message && (
      error.message.includes('locked') ||
      error.message.includes('already locked') ||
      error.message.includes('UploadAlreadyLockedException')
    )) {
      // 处理上传锁定冲突错误
      console.warn(`文件 ${file.name} 遇到上传锁定冲突，准备重试`)

      // 延迟重试，避免立即冲突
      setTimeout(() => {
        console.log(`重试锁定的上传文件: ${file.name}`)
        props.uppy.retryUpload(file.id)
      }, 1000 + Math.random() * 1000) // 1-2秒随机延迟

      // 显示友好的提示信息
      ElMessage.warning({
        message: `文件 ${file.name} 遇到并发上传冲突，正在自动重试...`,
        duration: 3000
      })

      return // 不执行后续的错误处理

    } else if (error.message && error.message.includes('offset')) {
      // 原有的 offset 错误处理
      console.warn(`文件 ${file.name} 遇到offset错误，尝试清理状态`)

      ElMessage.error({
        message: `文件 ${file.name} 上传失败：断点续传状态异常。建议重新选择文件上传。`,
        duration: 8000,
        showClose: true
      })

      // 尝试清理该文件的 tus 状态
      try {
        Object.keys(localStorage).forEach(key => {
          if (key.startsWith('tus::') && key.includes(file.id)) {
            localStorage.removeItem(key)
            console.log(`清理Tus状态: ${key}`)
          }
        })
      } catch (cleanupError) {
        console.error('清理Tus状态失败:', cleanupError)
      }
    } else {
      // 普通错误处理
      ElMessage.error({
        message: `文件 ${file.name} 上传失败: ${error.message}`,
        duration: 5000
      })
    }
  })

  // 上传完成
  props.uppy.on('complete', (result) => {
    console.log('上传完成:', result)
    isUploading.value = false
    showStats.value = false

    const { successful, failed } = result

    if (failed.length === 0) {
      // 全部成功
      uploadCompleted.value = true
      completionMessage.value = `成功上传 ${successful.length} 个文件`

      ElMessage.success({
        message: '所有文件上传完成！',
        duration: 3000
      })

      // 发送完成事件
      emit('upload-complete', {
        successful,
        failed,
        totalFiles: successful.length + failed.length
      })

      // 3秒后自动关闭弹窗
      setTimeout(() => {
        if (uploadCompleted.value) {
          handleClose()
        }
      }, 3000)

    } else if (successful.length === 0) {
      // 全部失败
      uploadFailed.value = true
      failureMessage.value = `${failed.length} 个文件上传失败`

      ElMessage.error({
        message: '文件上传失败，请检查网络连接或文件格式',
        duration: 5000
      })

      // 发送失败事件
      emit('upload-failed', {
        successful,
        failed,
        totalFiles: successful.length + failed.length
      })

    } else {
      // 部分成功
      uploadCompleted.value = true
      completionMessage.value = `成功上传 ${successful.length} 个文件，${failed.length} 个文件失败`

      ElMessage.warning({
        message: `部分文件上传完成：成功 ${successful.length} 个，失败 ${failed.length} 个`,
        duration: 5000
      })

      // 发送完成事件（包含失败信息）
      emit('upload-complete', {
        successful,
        failed,
        totalFiles: successful.length + failed.length
      })
    }
  })

  // 上传暂停
  props.uppy.on('upload-pause', () => {
    isPaused.value = true
    console.log('上传已暂停')
  })

  // 上传恢复
  props.uppy.on('upload-resume', () => {
    isPaused.value = false
    console.log('上传已恢复')
  })

  // 上传取消
  props.uppy.on('cancel-all', () => {
    isUploading.value = false
    isPaused.value = false
    showStats.value = false
    console.log('上传已取消')
  })
}

/**
 * 处理暂停/继续上传
 */
const handlePauseResume = () => {
  if (!props.uppy) return

  if (isPaused.value) {
    props.uppy.resumeAll()
  } else {
    props.uppy.pauseAll()
  }
}

/**
 * 处理取消上传
 */
const handleCancelUpload = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要取消当前上传吗？已上传的文件不会被删除。',
      '确认取消',
      {
        confirmButtonText: '确定取消',
        cancelButtonText: '继续上传',
        type: 'warning'
      }
    )

    if (props.uppy) {
      props.uppy.cancelAll()
    }

    ElMessage.info('上传已取消')

  } catch {
    // 用户取消确认，继续上传
  }
}

/**
 * 处理重试上传
 */
const handleRetry = () => {
  if (props.uppy) {
    uploadFailed.value = false
    failureMessage.value = ''
    props.uppy.retryAll()
  }
}

/**
 * 处理重新上传
 */
const handleReset = () => {
  if (props.uppy) {
    uploadCompleted.value = false
    completionMessage.value = ''

    // 清理所有 tus 相关的本地存储状态
    try {
      Object.keys(localStorage).forEach(key => {
        if (key.startsWith('tus::')) {
          localStorage.removeItem(key)
          console.log(`清理Tus状态: ${key}`)
        }
      })
      console.log('已清理所有TUS本地状态')

      ElMessage.success({
        message: '已清理上传状态，可以重新选择文件上传',
        duration: 3000
      })
    } catch (error) {
      console.error('清理TUS状态失败:', error)
    }

    props.uppy.reset()
    showStats.value = false
  }
}

/**
 * 运行网络诊断
 */
const runNetworkDiagnostics = async () => {
  ElMessage.info('正在运行网络诊断，请稍候...')

  try {
    const diagnostics = await runConnectionDiagnostics()

    // 检查是否需要登录
    if (!diagnostics.loginStatus.isLoggedIn) {
      await ElMessageBox.alert(
        '检测到您未登录系统，这是导致上传失败的主要原因。\n\n请先登录系统，然后重新尝试上传。',
        '需要登录',
        {
          confirmButtonText: '去登录',
          type: 'warning'
        }
      )

      // 重定向到登录页面
      redirectToLogin()
      return
    }

    // 格式化诊断结果
    const results = []

    // 登录状态
    results.push('=== 登录状态 ===')
    results.push(`✅ 用户已登录: ${diagnostics.loginStatus.userInfo?.userName || '未知用户'}`)
    results.push(`🔑 Token状态: ${diagnostics.loginStatus.token ? '有效' : '无效'}`)

    // 基本连接测试结果
    results.push('\n=== 基本连接测试 ===')
    if (diagnostics.basicTests) {
      diagnostics.basicTests.forEach(test => {
        const status = test.success ? '✅' : '❌'
        results.push(`${status} ${test.name}: ${test.status || 'N/A'} ${test.statusText || test.error || ''}`)
      })
    }

    // TUS协议测试结果
    results.push('\n=== TUS协议测试 ===')
    if (diagnostics.tusTests) {
      if (diagnostics.tusTests.success) {
        results.push('✅ TUS协议连接正常')
        results.push(`📡 状态端点: 正常`)
        results.push(`📡 OPTIONS预检: ${diagnostics.tusTests.optionsResponse.status}`)
        results.push(`📡 创建上传: ${diagnostics.tusTests.createResponse.status}`)
      } else {
        results.push(`❌ TUS协议连接失败: ${diagnostics.tusTests.error}`)
      }
    }

    // 诊断建议
    results.push('\n=== 诊断建议 ===')
    diagnostics.recommendations.forEach(rec => {
      results.push(`💡 ${rec}`)
    })

    // 显示详细的诊断结果
    await ElMessageBox.alert(
      results.join('\n'),
      '网络诊断结果',
      {
        confirmButtonText: '确定',
        type: diagnostics.basicTests?.every(t => t.success) && diagnostics.tusTests?.success ? 'success' : 'warning',
        customStyle: {
          'white-space': 'pre-line',
          'font-family': 'monospace',
          'text-align': 'left'
        }
      }
    )

    // 在控制台输出完整诊断信息
    console.log('🔍 完整网络诊断结果:', diagnostics)

  } catch (error) {
    console.error('💥 网络诊断失败:', error)
    ElMessage.error(`网络诊断失败: ${error.message}`)
  }
}

/**
 * 清理 TUS 上传状态
 */
const clearTusState = () => {
  try {
    // 清理 localStorage 中的 tus 状态
    Object.keys(localStorage).forEach(key => {
      if (key.startsWith('tus::')) {
        localStorage.removeItem(key)
        console.log(`清理Tus状态: ${key}`)
      }
    })

    // 重置所有状态
    uploadCompleted.value = false
    uploadFailed.value = false
    failureMessage.value = ''
    completionMessage.value = ''
    showStats.value = false

    if (props.uppy) {
      props.uppy.reset()
    }

    ElMessage.success({
      message: '上传状态已清理，请重新选择文件上传',
      duration: 3000
    })

    console.log('TUS状态清理完成')
  } catch (error) {
    console.error('清理TUS状态失败:', error)
    ElMessage.error('清理上传状态失败，请刷新页面重试')
  }
}

/**
 * 处理关闭
 */
const handleClose = () => {
  // 如果正在上传，需要确认
  if (isUploading.value) {
    ElMessageBox.confirm(
      '上传正在进行中，确定要关闭吗？',
      '确认关闭',
      {
        confirmButtonText: '确定关闭',
        cancelButtonText: '继续上传',
        type: 'warning'
      }
    ).then(() => {
      if (props.uppy) {
        props.uppy.cancelAll()
      }
      emit('close')
    }).catch(() => {
      // 用户取消关闭
    })
  } else {
    emit('close')
  }
}

/**
 * 测试 TUS 上传功能
 */
const testTusUpload = async () => {
  ElMessage.info('正在测试TUS上传功能，请稍候...')

  try {
    const result = await testTusUploadFunction()

    if (result.success) {
      ElMessage.success('TUS上传测试成功！')

      await ElMessageBox.alert(
        `测试结果：
✅ 上传成功
📁 文件大小: ${result.fileSize} 字节
📡 上传位置: ${result.uploadLocation}
📊 上传进度: ${result.uploadOffset}/${result.uploadLength}
🎯 完成状态: ${result.completed ? '已完成' : '未完成'}`,
        'TUS上传测试结果',
        {
          confirmButtonText: '确定',
          type: 'success'
        }
      )
    } else {
      ElMessage.error(`TUS上传测试失败: ${result.error}`)

      await ElMessageBox.alert(
        `测试失败：
❌ 错误信息: ${result.error}
💡 建议: 请检查网络连接和服务器配置`,
        'TUS上传测试失败',
        {
          confirmButtonText: '确定',
          type: 'error'
        }
      )
    }

  } catch (error) {
    console.error('💥 TUS上传测试异常:', error)
    ElMessage.error(`测试异常: ${error.message}`)
  }
}

// 监听弹窗显示状态
watch(() => props.visible, async (newValue, oldValue) => {
  console.log(`弹窗状态变化: ${oldValue} -> ${newValue}`)

  if (newValue) {
    // 弹窗打开时挂载 Dashboard
    console.log('弹窗打开，准备挂载 Dashboard')
    await nextTick()
    mountDashboard()
  } else {
    // 弹窗关闭时安全卸载 Dashboard
    console.log('弹窗关闭，准备卸载 Dashboard')
    unmountDashboard()
    // 重置所有状态
    resetComponentState()
  }
})

// 监听 uppy 实例变化
watch(() => props.uppy, (newUppy, oldUppy) => {
  console.log('Uppy 实例变化:', {
    old: oldUppy ? 'exists' : 'null',
    new: newUppy ? 'exists' : 'null',
    visible: props.visible
  })

  if (oldUppy && oldUppy !== newUppy) {
    // 如果有旧实例，先清理
    console.log('清理旧的 Uppy 实例')
    unmountDashboard()
  }

  if (newUppy && props.visible) {
    // 新实例且弹窗可见时，挂载 Dashboard
    console.log('新 Uppy 实例可用，挂载 Dashboard')
    nextTick(() => {
      mountDashboard()
    })
  }
})

/**
 * 重置组件状态
 */
const resetComponentState = () => {
  isUploading.value = false
  isPaused.value = false
  uploadCompleted.value = false
  uploadFailed.value = false
  showStats.value = false
  completionMessage.value = ''
  failureMessage.value = ''

  // 重置统计数据
  uploadStats.value = {
    totalFiles: 0,
    uploadedFiles: 0,
    failedFiles: 0,
    progress: 0
  }

  console.log('组件状态已重置')
}

// 组件卸载时安全清理
onUnmounted(() => {
  console.log('组件卸载，执行清理操作')
  unmountDashboard()
  resetComponentState()
})
</script>

<style scoped>
.uppy-dashboard-wrapper {
  min-height: 400px;
}

.upload-completion-section,
.upload-failure-section {
  margin-top: 20px;
  text-align: center;
}

.upload-stats {
  margin-top: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 6px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* Uppy Dashboard 样式优化 */
:deep(.uppy-Dashboard) {
  border: none;
  border-radius: 6px;
}

:deep(.uppy-Dashboard-inner) {
  border: 1px solid #dcdfe6;
  border-radius: 6px;
}

:deep(.uppy-Dashboard-AddFiles) {
  border-color: #dcdfe6;
}

:deep(.uppy-Dashboard-AddFiles:hover) {
  border-color: #409eff;
}

:deep(.uppy-Dashboard-AddFiles-title) {
  color: #606266;
}

:deep(.uppy-Dashboard-note) {
  color: #909399;
}

/* 隐藏图片预览，只显示文件名和大小 */
:deep(.uppy-Dashboard-Item-preview) {
  display: none !important;
}

:deep(.uppy-Dashboard-Item-previewIcon) {
  display: none !important;
}

/* 优化文件列表显示 */
:deep(.uppy-Dashboard-Item) {
  padding: 8px 12px !important;
  min-height: auto !important;
}

:deep(.uppy-Dashboard-Item-name) {
  font-size: 14px !important;
  font-weight: 500 !important;
  margin-bottom: 4px !important;
}

:deep(.uppy-Dashboard-Item-status) {
  font-size: 12px !important;
  color: #909399 !important;
}

/* 文件大小显示 */
:deep(.uppy-Dashboard-Item-statusSize) {
  font-size: 12px !important;
  color: #606266 !important;
  margin-left: 8px !important;
}

/* 进度条样式 */
:deep(.uppy-Dashboard-Item-progress) {
  height: 4px !important;
  margin-top: 8px !important;
}

/* 文件列表容器优化 */
:deep(.uppy-Dashboard-files) {
  max-height: 300px !important;
  overflow-y: auto !important;
}

/* 文件项布局优化 */
:deep(.uppy-Dashboard-Item-fileInfo) {
  flex: 1 !important;
  min-width: 0 !important;
}

/* 隐藏不必要的图标 */
:deep(.uppy-Dashboard-Item-previewIconWrap) {
  display: none !important;
}
  

</style>
