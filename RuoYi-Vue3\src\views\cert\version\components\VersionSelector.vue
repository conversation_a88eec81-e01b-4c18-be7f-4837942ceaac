<template>
  <div class="version-selector-container">
    <!-- 搜索筛选区域 -->
    <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="80px">
      <el-form-item label="国家" prop="countryCode">
        <CountrySelect v-model="queryParams.countryCode" placeholder="请选择国家" width="180px" />
      </el-form-item>
      <el-form-item label="证件类型" prop="certType">
        <CertTypeSelect v-model="queryParams.certType" placeholder="请选择证件类型" />
      </el-form-item>
      <el-form-item label="关键词" prop="keyword">
        <el-input
          v-model="queryParams.keyword"
          placeholder="搜索版本代码或描述"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 版本列表 -->
    <el-table v-loading="loading" :data="versionList" height="300px" stripe border>
      <el-table-column label="版本代码" prop="versionCode" width="180" show-overflow-tooltip />
      <el-table-column label="国家" prop="countryInfo.name" width="120" />
      <el-table-column label="证件类型" prop="certInfo.zjlbmc" width="150" show-overflow-tooltip />
      <el-table-column label="发行年份" prop="issueYear" width="100" align="center" />
      <el-table-column label="状态" prop="status" width="100" align="center">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.status)">{{ getStatusText(scope.row.status) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="100" align="center" fixed="right">
        <template #default="scope">
          <el-button type="primary" size="small" @click="handleSelect(scope.row)">选择</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
      layout="total, prev, pager, next"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus';

// 导入组件
import CountrySelect from '@/components/CertCommon/CountrySelect.vue';
import CertTypeSelect from '@/components/CertCommon/CertTypeSelect.vue';
import Pagination from '@/components/Pagination/index.vue';

// 导入API
import { getVersionList } from '@/api/samples/version';

const emit = defineEmits(['version-selected']);

// 响应式数据
const loading = ref(false);
const versionList = ref([]);
const total = ref(0);

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  countryCode: '',
  certType: '',
  keyword: ''
});

// 方法
const getList = async () => {
  loading.value = true;
  try {
    const response = await getVersionList(queryParams);
    if (response.code === 200) {
      versionList.value = response.data?.rows || response.rows || [];
      total.value = response.data?.total || response.total || 0;
    } else {
      ElMessage.error(response.msg || '获取版本列表失败');
    }
  } catch (error) {
    console.error('获取版本列表失败:', error);
    ElMessage.error('获取版本列表失败');
  } finally {
    loading.value = false;
  }
};

const handleQuery = () => {
  queryParams.pageNum = 1;
  getList();
};

const resetQuery = () => {
  queryParams.countryCode = '';
  queryParams.certType = '';
  queryParams.keyword = '';
  queryParams.pageNum = 1;
  getList();
};

const handleSelect = (row) => {
  emit('version-selected', row);
};

const getStatusType = (status) => {
  const statusMap = { active: 'success', inactive: 'info', pending: 'warning', disabled: 'danger' };
  return statusMap[status] || 'info';
};

const getStatusText = (status) => {
  const statusMap = { active: '启用', inactive: '停用', pending: '待审核', disabled: '禁用' };
  return statusMap[status] || '未知';
};

// 生命周期
onMounted(() => {
  getList();
});
</script>

<style scoped>
.version-selector-container {
  padding: 10px;
}
</style> 