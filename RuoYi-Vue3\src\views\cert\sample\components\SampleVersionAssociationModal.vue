<template>
  <el-dialog
    :model-value="modelValue"
    :title="isChangeMode ? '更换版本关联' : '样本版本关联'"
    width="60%"
    :close-on-click-modal="false"
    @update:model-value="$emit('update:modelValue', $event)"
    @open="handleOpen"
  >
    <!-- 文件夹信息 -->
    <el-card shadow="never" class="info-card">
      <template #header><span>待关联文件夹</span></template>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="文件夹名称">{{ targetFolder.folderName }}</el-descriptions-item>
        <el-descriptions-item label="文件数量">{{ targetFolder.fileCount }}</el-descriptions-item>
        <el-descriptions-item label="国家">{{ targetFolder.countryInfo?.name }}</el-descriptions-item>
        <el-descriptions-item label="证件类型">{{ targetFolder.certInfo?.zjlbmc }}</el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 当前关联版本信息 (仅更换模式) -->
    <el-card v-if="isChangeMode && currentVersion" shadow="never" class="info-card">
      <template #header><span>当前已关联版本</span></template>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="版本代码">{{ currentVersion.versionCode }}</el-descriptions-item>
        <el-descriptions-item label="发行年份">{{ currentVersion.issueYear }}</el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 选项卡 -->
    <el-tabs v-model="activeTab" class="tabs-container">
      <el-tab-pane label="推荐版本" name="recommended">
        <div class="tab-pane-content">
          <div class="toolbar">
            <el-button icon="Cpu" @click="handleSmartDetect">智能检测</el-button>
            <el-button icon="Plus" @click="showCreateVersion = true">创建新版本</el-button>
          </div>
          
          <!-- 推荐版本列表 -->
          <el-table v-loading="recommendLoading" :data="recommendedVersions" height="250px" stripe>
            <el-table-column prop="versionCode" label="版本代码" />
            <el-table-column prop="matchReason" label="推荐理由" />
            <el-table-column prop="matchScore" label="匹配度">
              <template #default="{ row }">
                <el-progress :percentage="row.matchScore * 100" />
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template #default="{ row }">
                <el-button type="primary" size="small" @click="handleConfirmAssociation(row)">选择此版本</el-button>
              </template>
            </el-table-column>
          </el-table>
          
          <!-- 创建新版本区域 -->
          <div v-if="showCreateVersion" class="create-version-section">
            <el-divider content-position="left">创建新版本</el-divider>
            <el-form :model="newVersionForm" label-width="100px" inline>
              <el-form-item label="版本代码" required>
                <el-input 
                  v-model="newVersionForm.versionCode" 
                  placeholder="请输入版本代码"
                  style="width: 300px"
                />
                <el-button type="primary" @click="handleCreateVersion">创建并关联</el-button>
                <el-button @click="showCreateVersion = false">取消</el-button>
              </el-form-item>
            </el-form>
          </div>
          
          <el-empty v-if="!recommendLoading && recommendedVersions.length === 0 && !showCreateVersion" description="暂无推荐版本" />
        </div>
      </el-tab-pane>

      <el-tab-pane label="浏览所有版本" name="browse">
        <VersionSelector @version-selected="handleConfirmAssociation" />
      </el-tab-pane>
    </el-tabs>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="$emit('update:modelValue', false)">取消</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { Cpu, Plus } from '@element-plus/icons-vue';
import VersionSelector from '@/views/cert/version/components/VersionSelector.vue';
import { findSimilarVersions, createVersionFromFolder } from '@/api/samples/version'; // 取消注释
import { associateFolderToVersion } from '@/api/samples/folder';

const props = defineProps({
  modelValue: Boolean,
  targetFolder: {
    type: Object,
    default: () => ({})
  },
  isChangeMode: {
    type: Boolean,
    default: false
  },
  currentVersion: {
    type: Object,
    default: null
  }
});

const emit = defineEmits(['update:modelValue', 'association-changed']);

const activeTab = ref('recommended');
const recommendLoading = ref(false);
const recommendedVersions = ref([]);
const showCreateVersion = ref(false);

// 新版本表单
const newVersionForm = ref({
  versionCode: ''
});

// 生成默认版本代码
const generateDefaultVersionCode = () => {
  const { countryInfo, certInfo, issueYear } = props.targetFolder;
  if (countryInfo?.code && certInfo?.zjlbdm && issueYear) {
    return `${countryInfo.code}_${certInfo.zjlbdm}_${issueYear}_${Date.now().toString().slice(-4)}`;
  }
  return `VERSION_${Date.now()}`;
};

const handleOpen = () => {
  activeTab.value = props.isChangeMode ? 'browse' : 'recommended';
  fetchRecommendedVersions();
  // 预生成版本代码
  newVersionForm.value.versionCode = generateDefaultVersionCode();
};

const fetchRecommendedVersions = async () => {
  console.log('=== fetchRecommendedVersions 开始 ===');
  console.log('props.targetFolder:', props.targetFolder);
  
  if (!props.targetFolder?.folderId) {
    console.log('没有 folderId，退出');
    recommendLoading.value = false;
    return;
  }
  
  recommendLoading.value = true;
  try {
    console.log('=== 开始处理推荐版本 ===');
    
    // 获取预解析信息
    const preParseInfo = props.targetFolder.preParseVersionInfo;
    console.log('preParseInfo:', preParseInfo);
    
    if (preParseInfo && preParseInfo.parsedVersionCode) {
      console.log('找到 parsedVersionCode:', preParseInfo.parsedVersionCode);
      
      // 构建推荐版本对象
      const recommendedVersion = {
        versionId: null, // 新版本，还没有ID
        versionCode: preParseInfo.parsedVersionCode,
        matchReason: '基于文件夹名称解析生成',
        matchScore: 0.95, // 高匹配度
        isNewVersion: true // 标记为新版本
      };
      
      recommendedVersions.value = [recommendedVersion];
      console.log('设置推荐版本:', recommendedVersion);
    } else {
      console.log('未找到 parsedVersionCode');
      recommendedVersions.value = [];
    }
    
  } catch (error) {
    console.error('获取推荐版本失败:', error);
    console.error('错误堆栈:', error.stack);
    recommendedVersions.value = [];
  } finally {
    recommendLoading.value = false;
    console.log('=== fetchRecommendedVersions 结束 ===');
  }
};

const handleConfirmAssociation = async (version) => {
  try {
    // TODO: 等待后端API实现后取消注释
    // const response = await associateFolderToVersion(props.targetFolder.folderId, version.versionId);
    
    // 模拟成功响应
    const response = { code: 200, msg: '关联成功' };
    
    if (response.code === 200) {
      ElMessage.success(`文件夹已成功关联到版本: ${version.versionCode}`);
      emit('association-changed');
      emit('update:modelValue', false);
    } else {
      ElMessage.error(response.msg || '关联失败');
    }
  } catch (error) {
    ElMessage.error('关联操作失败');
  }
};

const handleCreateVersion = async () => {
  if (!newVersionForm.value.versionCode.trim()) {
    ElMessage.warning('请输入版本代码');
    return;
  }

  try {
    // 创建新版本并关联
    const createData = {
      folderId: props.targetFolder.folderId,
      versionCode: newVersionForm.value.versionCode,
      description: `基于文件夹"${props.targetFolder.folderName}"创建的版本`
    };

    const response = await createVersionFromFolder(createData);
    
    if (response.code === 200) {
      ElMessage.success(`新版本"${newVersionForm.value.versionCode}"创建并关联成功`);
      emit('association-changed');
      emit('update:modelValue', false);
    } else {
      ElMessage.error(response.msg || '创建版本失败');
    }
  } catch (error) {
    console.error('创建版本失败:', error);
    ElMessage.error('创建版本失败: ' + (error.message || error));
  }
};

const handleSmartDetect = () => {
  ElMessage.info('智能检测功能开发中...');
};
</script>

<style scoped>
.info-card {
  margin-bottom: 20px;
}
.tabs-container {
  margin-top: -10px;
}
.tab-pane-content {
  padding: 10px;
}
.toolbar {
  margin-bottom: 10px;
}
.create-version-section {
  margin-top: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 4px;
}
</style> 