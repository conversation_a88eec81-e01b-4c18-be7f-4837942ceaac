<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`基于${props.sourceFolder?.folderName || ''}创建新版本`"
    width="700px"
    :before-close="handleClose"
  >
    <!-- 源文件夹信息展示 -->
    <div class="source-info">
      <h4 class="info-title">以下信息将自动继承到新版本：</h4>
      <div class="info-grid">
        <div class="info-item">
          <span class="label">国家：</span>
          <span class="value">{{ props.sourceFolder?.countryInfo?.name || '-' }}</span>
        </div>
        <div class="info-item">
          <span class="label">证件类型：</span>
          <span class="value">{{ props.sourceFolder?.certInfo?.zjlbmc || '-' }}</span>
        </div>
        <div class="info-item">
          <span class="label">年份：</span>
          <span class="value">{{ props.sourceFolder?.issueYear || '-' }}</span>
        </div>
        <div class="info-item">
          <span class="label">文件数量：</span>
          <span class="value">{{ props.sourceFolder?.fileCount || 0 }} 个</span>
        </div>
      </div>
    </div>

    <!-- 新版本信息表单 -->
    <div class="form-section">
      <h4 class="form-title">请填写新版本的独特信息：</h4>
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        label-position="left"
      >
        <el-form-item label="版本代码" prop="versionCode">
          <el-input
            v-model="formData.versionCode"
            placeholder="系统已自动生成版本代码，您可以修改"
            clearable
            :maxlength="100"
            show-word-limit
          />
          <div class="form-hint">版本代码格式：国家代码_证件类型代码_年份_证号前缀_签发地</div>
        </el-form-item>

        <el-form-item label="版本描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            placeholder="请输入版本描述，说明该版本的特点或用途"
            :rows="4"
            :maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作按钮 -->
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose" :disabled="loading">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="loading">
          创建并关联
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
// 导入API
import { createVersionFromFolder } from '@/api/samples/version'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  sourceFolder: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'creation-success'])

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const formRef = ref()



// 表单数据
const formData = reactive({
  versionCode: '',
  description: ''
})

// 表单验证规则
const formRules = {
  versionCode: [
    { required: true, message: '请输入版本代码', trigger: 'blur' },
    { min: 3, max: 100, message: '版本代码长度应在 3 到 100 个字符之间', trigger: 'blur' },
    { pattern: /^[A-Za-z0-9\-_\u4e00-\u9fa5]+$/, message: '版本代码只能包含字母、数字、短横线、下划线和中文', trigger: 'blur' }
  ],
  description: [
    { max: 500, message: '描述长度不能超过 500 个字符', trigger: 'blur' }
  ]
}

// 监听 modelValue 变化
watch(() => props.modelValue, (newVal) => {
  dialogVisible.value = newVal
  if (newVal) {
    resetForm()
    // 可以基于源文件夹信息预填一些字段
    if (props.sourceFolder) {
      formData.versionCode = generateVersionCode()
    }
  }
})

// 监听 dialogVisible 变化
watch(dialogVisible, (newVal) => {
  if (!newVal) {
    emit('update:modelValue', false)
  }
})

// 生成建议的版本代码
function generateVersionCode() {
  const folder = props.sourceFolder
  if (!folder) return ''

  // 从文件夹名称解析信息：西班牙_公务普通护照_2001_XDD85_哈瓦那
  const folderName = folder.folderName || ''
  const parts = folderName.split('_')

  if (parts.length >= 5) {
    const countryCode = folder.countryInfo?.code || parts[0]
    const certTypeCode = folder.certInfo?.zjlbdm || parts[1]
    const issueYear = parts[2]
    const certNumberPrefix = parts[3]
    const issuePlace = parts[4]

    // 格式：ESP_13_2001_XDD85_哈瓦那
    return `${countryCode}_${certTypeCode}_${issueYear}_${certNumberPrefix}_${issuePlace}`
  }

  // 备选方案：使用现有数据
  const countryCode = folder.countryInfo?.code || 'XX'
  const certTypeCode = folder.certInfo?.zjlbdm || 'XX'
  const issueYear = folder.issueYear || new Date().getFullYear()

  return `${countryCode}_${certTypeCode}_${issueYear}`
}



// 重置表单
function resetForm() {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  formData.versionCode = ''
  formData.description = ''
}

// 事件处理函数
function handleClose() {
  dialogVisible.value = false
}

async function handleConfirm() {
  if (!formRef.value) return

  try {
    // 验证表单
    await formRef.value.validate()

    loading.value = true

    // 构造DTO，后端自动获取创建者信息
    const dto = {
      folderId: props.sourceFolder.folderId,
      versionCode: formData.versionCode.trim(),
      description: formData.description.trim()
    }

    console.log('创建版本DTO:', dto)

    // 调用创建API
    const result = await createVersionFromFolder(dto)

    if (result.code === 200) {
      ElMessage.success(`版本 "${formData.versionCode}" 创建成功`)
      emit('creation-success', { versionCode: formData.versionCode })
      handleClose()
    } else {
      ElMessage.error(result.msg || '创建失败')
    }
  } catch (error) {
    console.error('创建版本失败:', error)
    if (error.message !== 'Validation failed') {
      ElMessage.error('创建版本失败: ' + (error.message || '未知错误'))
    }
  } finally {
    loading.value = false
  }
}
</script>

<style scoped lang="scss">
.source-info {
  margin-bottom: 24px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #409eff;

  .info-title {
    margin: 0 0 12px 0;
    font-size: 14px;
    font-weight: 600;
    color: #303133;
  }

  .info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 8px 16px;
  }

  .info-item {
    display: flex;
    align-items: center;

    .label {
      font-weight: 500;
      color: #606266;
      margin-right: 8px;
      min-width: 80px;
    }

    .value {
      color: #303133;
      font-weight: 600;
    }
  }
}

.form-section {
  .form-title {
    margin: 0 0 16px 0;
    font-size: 14px;
    font-weight: 600;
    color: #303133;
  }

  .form-hint {
    margin-top: 4px;
    font-size: 12px;
    color: #909399;
    line-height: 1.4;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-textarea__inner) {
  resize: vertical;
}
</style>
