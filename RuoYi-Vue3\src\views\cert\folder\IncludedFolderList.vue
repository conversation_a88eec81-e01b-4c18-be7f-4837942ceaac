<template>
  <div class="unassigned-folder-list">
    <div v-loading="loading" class="folder-list">
      <FolderListItem
        v-for="folder in unassignedFolders"
        :key="folder.folderId"
        :folder-name="folder.folderName"
        :thumbnail-url="folder.mainPicPath"
        :show-checkbox="true"
        :is-checked="selectedFolders.includes(folder.folderId)"
        :show-compare-button="true"
        @check-change="handleSelection(folder.folderId)"
        @compare-clicked="openCompareModal(folder)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import FolderListItem from '@/components/FolderListItem/index.js'
import { getFolderList, type FolderInfoVO } from '@/api/samples/folder'

// 响应式数据
const loading = ref(false)
/** @type {import('@/api/samples/folder').FolderInfoVO[]} */
const unassignedFolders = ref([])
const selectedFolders = ref([])

// 获取未分配的文件夹数据
const loadUnassignedFolders = async () => {
  loading.value = true
  try {
    const response = await getFolderList({ status: 'unassociated' })
    if (response.code === 200) {
      unassignedFolders.value = response.rows || []
    } else {
      ElMessage.error(response.msg || '加载未分配文件夹失败')
    }
  } catch (error) {
    console.error('加载未分配文件夹失败:', error)
    ElMessage.error('加载未分配文件夹失败')
  } finally {
    loading.value = false
  }
}

// 处理文件夹选择
const handleSelection = (folderId: string) => {
  const index = selectedFolders.value.indexOf(folderId)
  if (index > -1) {
    selectedFolders.value.splice(index, 1)
  } else {
    selectedFolders.value.push(folderId)
  }
}

// 打开比对模态框
const openCompareModal = (folder: FolderInfoVO) => {
  console.log('打开比对模态框:', folder)
  // TODO: 实现比对功能
}

// 生命周期
onMounted(() => {
  loadUnassignedFolders()
})
</script>

<style scoped>
.unassigned-folder-list {
  padding: 20px;
}

.folder-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}
</style>
