<template>
  <div class="app-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="page-title">
        <h2>版本管理</h2>
        <p>管理证件版本信息，设置标准样本文件夹</p>
      </div>
    </div>

    <!-- 搜索筛选区域 -->
    <el-card class="search-card" shadow="never">
      <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="80px">
        <el-form-item label="国家" prop="countryCode">
          <CountrySelect
            v-model="queryParams.countryCode"
            placeholder="请选择国家"
            width="200px"
            @change="handleCountryChange"
          />
        </el-form-item>
        
        <el-form-item label="证件类型" prop="certType">
          <CertTypeSelect
            v-model="queryParams.certType"
            placeholder="请选择证件类型"
            @change="handleCertTypeChange"
          />
        </el-form-item>
        
        <el-form-item label="发行年份" prop="issueYear">
          <el-select
            v-model="queryParams.issueYear"
            placeholder="请选择年份"
            clearable
            style="width: 120px"
          >
            <el-option
              v-for="year in yearOptions"
              :key="year"
              :label="year"
              :value="year"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="关键词" prop="keyword">
          <el-input
            v-model="queryParams.keyword"
            placeholder="搜索版本代码或描述"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 版本列表 -->
    <el-card class="table-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>版本列表</span>
          <div class="header-actions">
            <el-button type="primary" icon="Plus" @click="handleAdd">新增版本</el-button>
          </div>
        </div>
      </template>

      <el-table
        v-loading="loading"
        :data="versionList"
        @selection-change="handleSelectionChange"
        stripe
        border
      >
        <el-table-column type="selection" width="55" align="center" />
        
        <el-table-column label="版本代码" prop="versionCode" width="180" show-overflow-tooltip>
          <template #default="scope">
            <el-link type="primary" @click="handleDetail(scope.row)">
              {{ scope.row.versionCode }}
            </el-link>
          </template>
        </el-table-column>
        
        <el-table-column label="国家" prop="countryInfo" width="120" align="center">
          <template #default="scope">
            <div class="country-cell">
              <span>{{ scope.row.countryInfo?.name || '-' }}</span>
              <el-text type="info" size="small">{{ scope.row.countryInfo?.code || '-' }}</el-text>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="证件类型" prop="certInfo" width="150" align="center">
          <template #default="scope">
            <div class="cert-type-cell">
              <span>{{ scope.row.certInfo?.zjlbmc || '-' }}</span>
              <el-text type="info" size="small">{{ scope.row.certInfo?.zjlbdm || '-' }}</el-text>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="发行年份" prop="issueYear" width="100" align="center" />
        
        <el-table-column label="关联文件夹" width="120" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.folderCount > 0 ? 'success' : 'info'">
              {{ scope.row.folderCount || 0 }} 个
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="标准样本状态" width="140" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row.standardFolderId" type="success">
              已设置
            </el-tag>
            <el-tag v-else type="warning">
              未设置
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="状态" prop="status" width="100" align="center">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="创建时间" prop="createTime" width="160" align="center">
          <template #default="scope">
            {{ formatTime(scope.row.createTime) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" align="center" fixed="right">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              @click="handleDetail(scope.row)"
            >
              查看详情
            </el-button>
            <el-button
              type="warning"
              size="small"
              @click="handleManageFolders(scope.row)"
            >
              管理文件夹
            </el-button>
            <el-dropdown @command="(command) => handleDropdownCommand(command, scope.row)">
              <el-button type="info" size="small">
                更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="edit">编辑版本</el-dropdown-item>
                  <el-dropdown-item command="delete" divided>删除版本</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 版本详情弹窗 -->
    <VersionDetailModal
      v-model="showDetailModal"
      :version-info="currentVersion"
      @refresh="getList"
    />

    <!-- 文件夹管理弹窗 -->
    <FolderManagementModal
      v-model="showFolderModal"
      :version-info="currentVersion"
      @refresh="getList"
    />

    <!-- 版本文件夹管理弹窗 -->
    <VersionFolderManagementModal
      v-model="showVersionFolderModal"
      :version-info="currentVersion"
      @refresh="getList"
      @standard-changed="handleStandardChanged"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Plus, ArrowDown } from '@element-plus/icons-vue'

// 导入组件
import CountrySelect from '@/components/CertCommon/CountrySelect.vue'
import CertTypeSelect from '@/components/CertCommon/CertTypeSelect.vue'
import VersionDetailModal from './components/VersionDetailModal.vue'
import FolderManagementModal from './components/FolderManagementModal.vue'
import VersionFolderManagementModal from './components/VersionFolderManagementModal.vue'
import Pagination from '@/components/Pagination/index.vue'

// 导入API - 使用统一的新API
import { getVersionList, getVersionDetails, deleteVersion } from '@/api/samples/version'

// 响应式数据
const loading = ref(false)
const showDetailModal = ref(false)
const showFolderModal = ref(false)
const showVersionFolderModal = ref(false)
const currentVersion = ref({})
const versionList = ref([])
const selectedVersions = ref([])
const total = ref(0)

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  countryCode: '',
  certType: '',
  issueYear: '',
  keyword: ''
})

// 年份选项（最近20年）
const yearOptions = computed(() => {
  const currentYear = new Date().getFullYear()
  const years = []
  for (let i = currentYear; i >= currentYear - 20; i--) {
    years.push(i.toString())
  }
  return years
})

// 方法
const getList = async () => {
  loading.value = true
  try {
    const params = {
      pageNum: queryParams.pageNum,
      pageSize: queryParams.pageSize
    }

    // 添加筛选条件
    if (queryParams.countryCode) params.countryCode = queryParams.countryCode
    if (queryParams.certType) params.certType = queryParams.certType
    if (queryParams.issueYear) params.issueYear = queryParams.issueYear
    if (queryParams.keyword) params.keyword = queryParams.keyword

    // 使用新的版本管理API
    const response = await getVersionList(params)
    if (response.code === 200) {
      versionList.value = response.data?.rows || response.rows || []
      total.value = response.data?.total || response.total || 0
    } else {
      ElMessage.error(response.msg || '获取版本列表失败')
    }
  } catch (error) {
    console.error('获取版本列表失败:', error)
    ElMessage.error('获取版本列表失败')
  } finally {
    loading.value = false
  }
}

const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

const resetQuery = () => {
  queryParams.countryCode = ''
  queryParams.certType = ''
  queryParams.issueYear = ''
  queryParams.keyword = ''
  queryParams.pageNum = 1
  getList()
}

const handleCountryChange = () => {
  // 国家变化时可以重新加载数据
  handleQuery()
}

const handleCertTypeChange = () => {
  // 证件类型变化时可以重新加载数据
  handleQuery()
}

const handleSelectionChange = (selection) => {
  selectedVersions.value = selection
}

const handleAdd = () => {
  ElMessage.info('新增版本功能开发中...')
}

const handleDetail = (row) => {
  currentVersion.value = row
  showDetailModal.value = true
}

const handleManageFolders = (row) => {
  currentVersion.value = row
  showVersionFolderModal.value = true
}

const handleDropdownCommand = (command, row) => {
  switch (command) {
    case 'edit':
      handleEdit(row)
      break
    case 'delete':
      handleDelete(row)
      break
  }
}

const handleEdit = (row) => {
  ElMessage.info('编辑版本功能开发中...')
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除版本"${row.versionCode}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const response = await delCertVersion(row.id)
    if (response.code === 200) {
      ElMessage.success('删除成功')
      getList()
    } else {
      ElMessage.error(response.msg || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除版本失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const getStatusType = (status) => {
  const statusMap = {
    'active': 'success',
    'inactive': 'info',
    'pending': 'warning',
    'disabled': 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    'active': '启用',
    'inactive': '停用',
    'pending': '待审核',
    'disabled': '禁用'
  }
  return statusMap[status] || '未知'
}

const formatTime = (time) => {
  if (!time) return '-'
  return new Date(time).toLocaleString('zh-CN')
}

const handleStandardChanged = (data) => {
  // 更新版本列表中对应版本的标准样本状态
  const version = versionList.value.find(v => v.versionId === data.versionId)
  if (version) {
    version.standardFolderId = data.standardFolderId
  }

  // 显示成功消息
  if (data.standardFolderId) {
    ElMessage.success(`已将"${data.folderName}"设为标准样本`)
  } else {
    ElMessage.success('已取消标准样本设置')
  }
}

// 生命周期
onMounted(() => {
  getList()
})
</script>

<style scoped>
.page-header {
  margin-bottom: 20px;
}

.page-title h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-title p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.country-cell,
.cert-type-cell {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.country-cell span,
.cert-type-cell span {
  font-weight: 500;
}
</style>
