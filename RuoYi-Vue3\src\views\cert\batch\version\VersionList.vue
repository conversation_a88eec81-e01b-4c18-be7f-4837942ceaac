<template>
  <div class="version-list">
    <div class="list-header">
      <div class="header-left">
        <h4>版本列表</h4>
        <el-tag type="info" size="small">{{ versions.length }} 个版本</el-tag>
      </div>
      <div class="header-right">
        <el-button 
          type="primary" 
          size="small"
          @click="$emit('create-version-clicked')"
          :disabled="!canCreateVersion"
        >
          <el-icon><Plus /></el-icon>
          创建版本
        </el-button>
      </div>
    </div>

    <!-- 版本列表 -->
    <div class="version-items">
      <el-empty 
        v-if="versions.length === 0"
        description="暂无版本数据"
        :image-size="80"
      />
      
      <div 
        v-else
        v-for="version in versions"
        :key="version.versionId"
        class="version-item"
      >
        <div class="version-info">
          <div class="version-header">
            <h5>{{ version.versionCode }}</h5>
            <el-tag :type="getVersionStatusType(version.status)" size="small">
              {{ getVersionStatusText(version.status) }}
            </el-tag>
          </div>
          <p class="version-description">{{ version.description || '暂无描述' }}</p>
          <div class="version-meta">
            <span>创建时间: {{ formatDateTime(version.createTime) }}</span>
            <span>文件夹数: {{ version.folderCount || 0 }}</span>
          </div>
        </div>
        
        <div class="version-actions">
          <el-button 
            type="primary" 
            size="small" 
            text
            @click="handleViewVersion(version)"
          >
            查看
          </el-button>
          <el-button 
            type="danger" 
            size="small" 
            text
            @click="handleDeleteVersion(version)"
          >
            删除
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

// Props
interface Props {
  taskId: string
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'create-version-clicked': []
}>()

// 响应式数据
const versions = ref([])
const canCreateVersion = ref(true)

// 获取版本列表
const loadVersions = async () => {
  try {
    // TODO: 替换为实际的API调用
    // const response = await getVersionsByTaskId(props.taskId)
    // versions.value = response.data || []
    
    // 模拟数据
    versions.value = [
      {
        versionId: 'v1',
        versionCode: 'V1.0.0',
        description: '第一个版本',
        status: 'ACTIVE',
        createTime: new Date().toISOString(),
        folderCount: 5
      }
    ]
  } catch (error) {
    ElMessage.error('加载版本列表失败')
  }
}

// 查看版本
const handleViewVersion = (version) => {
  console.log('查看版本:', version)
  ElMessage.info('版本详情功能开发中...')
}

// 删除版本
const handleDeleteVersion = async (version) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除版本 "${version.versionCode}" 吗？`,
      '确认删除',
      { type: 'warning' }
    )
    
    // TODO: 调用删除API
    // await deleteVersion(version.versionId)
    
    ElMessage.success('版本删除成功')
    loadVersions()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除版本失败')
    }
  }
}

// 获取版本状态类型
const getVersionStatusType = (status) => {
  const statusMap = {
    'ACTIVE': 'success',
    'INACTIVE': 'info',
    'DRAFT': 'warning'
  }
  return statusMap[status] || 'info'
}

// 获取版本状态文本
const getVersionStatusText = (status) => {
  const statusMap = {
    'ACTIVE': '活跃',
    'INACTIVE': '非活跃',
    'DRAFT': '草稿'
  }
  return statusMap[status] || status
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 暴露方法给父组件
defineExpose({
  loadVersions
})

// 生命周期
onMounted(() => {
  loadVersions()
})
</script>

<style scoped>
.version-list {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e4e7ed;
  background-color: #fafafa;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-left h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.version-items {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.version-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  margin-bottom: 12px;
  background-color: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  transition: all 0.3s;
}

.version-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.version-info {
  flex: 1;
}

.version-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.version-header h5 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.version-description {
  margin: 0 0 8px 0;
  color: #606266;
  font-size: 14px;
}

.version-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #909399;
}

.version-actions {
  display: flex;
  gap: 8px;
}
</style> 