<template>
  <div class="batch-task-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>批量上传任务管理</h2>
        <p class="page-description">管理证件样本的批量上传任务，支持断点续传</p>
      </div>
      <div class="header-right">
        <el-button
          type="primary"
          size="large"
          :icon="Upload"
          @click="handleNewUploadTask"
          :loading="isCreatingTask"
          v-hasPermi="['cert:batch:add']"
        >
          新建上传任务
        </el-button>
      </div>
    </div>

    <!-- 任务列表 -->
    <div class="task-list-section">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>任务列表</span>
            <el-button
              text
              :icon="Refresh"
              @click="refreshTaskList"
              :loading="tableLoading"
            >
              刷新
            </el-button>
          </div>
        </template>

        <!-- 搜索筛选 -->
        <div class="search-section">
          <el-form :model="searchForm" inline>
            <el-form-item label="任务状态">
              <el-select
                v-model="searchForm.status"
                placeholder="全部状态"
                clearable
                style="width: 150px"
              >
                <el-option label="上传中" value="UPLOADING" />
                <el-option label="已完成" value="COMPLETED" />
                <el-option label="失败" value="FAILED" />
                <el-option label="已取消" value="CANCELLED" />
              </el-select>
            </el-form-item>
            <el-form-item label="任务名称">
              <el-input
                v-model="searchForm.taskName"
                placeholder="请输入任务名称"
                clearable
                style="width: 200px"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">搜索</el-button>
              <el-button @click="handleResetSearch">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 任务表格 -->
        <el-table
          :data="taskList"
          :loading="tableLoading"
          stripe
          style="width: 100%"
        >
          <el-table-column prop="taskName" label="任务名称" min-width="200">
            <template #default="{ row }">
              <div class="task-name-cell">
                <span class="task-name">{{ row.taskName }}</span>
                <el-tag size="small" class="task-id-tag">{{ row.taskId }}</el-tag>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="status" label="状态" width="120">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="进度" width="200">
            <template #default="{ row }">
              <div class="progress-cell">
                <el-progress
                  :percentage="getProgressPercentage(row)"
                  :status="getProgressStatus(row.status)"
                  :stroke-width="8"
                />
                <span class="progress-text">
                  {{ row.processedFiles }}/{{ row.totalFiles }} 文件
                </span>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="issueYear" label="签发年份" width="100" />
          <el-table-column prop="issuePlace" label="签发地" width="120" />
          <el-table-column prop="createTime" label="创建时间" width="180">
            <template #default="{ row }">
              {{ formatDateTime(row.createTime) }}
            </template>
          </el-table-column>

          <el-table-column label="操作" width="280" fixed="right">
            <template #default="{ row }">
              <el-button
                v-if="row.status === 'UPLOADING'"
                type="warning"
                size="small"
                @click="handlePauseTask(row)"
                v-hasPermi="['cert:batch:edit']"
              >
                暂停
              </el-button>
              <el-button
                v-if="row.status === 'FAILED'"
                type="primary"
                size="small"
                @click="handleRetryTask(row)"
                v-hasPermi="['cert:batch:edit']"
              >
                重试
              </el-button>
              <el-button
                type="success"
                size="small"
                @click="handleOpenWorkspace(row)"
                v-hasPermi="['cert:batch:query']"
              >
                工作台
              </el-button>
              <el-button
                type="info"
                size="small"
                @click="handleViewTask(row)"
                v-hasPermi="['cert:batch:query']"
              >
                详情
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click="handleDeleteTask(row)"
                v-hasPermi="['cert:batch:remove']"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-section">
          <el-pagination
            v-model:current-page="pagination.pageNum"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 隐藏的文件夹选择器 -->
    <input
      ref="folderInputRef"
      type="file"
      webkitdirectory
      multiple
      style="display: none"
      @change="handleFolderSelected"
    />

    <!-- 统一使用多版本元数据确认弹窗 -->
    <MultiVersionMetadataModal
      :visible="showMultiVersionModal"
      :folder-structure="selectedFolderStructure"
      @close="showMultiVersionModal = false"
      @submit="handleMultiVersionSubmit"
    />

    <!-- 文件夹扫描进度弹窗 -->
    <FolderScanProgress
      :visible="showScanProgress"
      :scan-progress="scanProgress"
      :is-scanning="isScanning"
      :stats="scanStats"
      :current-file="currentScanFile"
      :error-message="scanErrorMessage"
      @close="handleScanProgressClose"
      @cancel="handleScanCancel"
    />

    <!-- Uppy 上传进度弹窗 -->
    <UppyDashboardModal
      :visible="showUploadModal"
      :uppy="uppy"
      @close="handleUploadModalClose"
      @upload-complete="handleUploadComplete"
      @upload-failed="handleUploadFailed"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Upload, Refresh } from '@element-plus/icons-vue'
import { useBatchTasks } from '@/composables/useBatchTasks'

// 路由实例
const router = useRouter()
import {
  createBatchTask,
  getBatchTaskList,
  deleteBatchTask,
  restartBatchTask,
  getBatchTaskStats,
  getBatchTasksByStatus,
  updateBatchTaskStatus,
  retryBatchTask,
  pauseBatchTask,
  resumeBatchTask
} from '@/api/cert/batchTask'
import MultiVersionMetadataModal from './components/MultiVersionMetadataModal.vue'
import UppyDashboardModal from './components/UppyDashboardModal.vue'
import FolderScanProgress from './components/FolderScanProgress.vue'
import { BatchTaskErrorHandler, withErrorHandler } from '@/utils/errorHandler'

// 使用 Uppy Composable
const { uppy, resetUppy, createUppy } = useBatchTasks()

// 页面状态
const isCreatingTask = ref(false)
const tableLoading = ref(false)
const showUploadModal = ref(false)

// 文件夹选择
const folderInputRef = ref(null)
const selectedFiles = ref([])
const selectedFolderStructure = ref([])
const showMultiVersionModal = ref(false)

// 文件夹扫描进度
const showScanProgress = ref(false)
const scanProgress = ref(0)
const isScanning = ref(false)
const scanStats = ref({
  foldersFound: 0,
  filesFound: 0,
  currentFolder: ''
})
const currentScanFile = ref('')
const scanErrorMessage = ref('')

// 搜索表单
const searchForm = reactive({
  status: '',
  taskName: ''
})

// 任务列表
const taskList = ref([])

// 分页
const pagination = reactive({
  pageNum: 1,
  pageSize: 20,
  total: 0
})





/**
 * 处理新建上传任务
 */
const handleNewUploadTask = () => {
  if (folderInputRef.value) {
    folderInputRef.value.click()
  }
}

/**
 * 处理文件夹选择
 */
const handleFolderSelected = async (event) => {
  const files = Array.from(event.target.files)
  if (files.length === 0) return

  console.log('选择的文件:', files)

  // 显示扫描进度弹窗
  showScanProgress.value = true
  isScanning.value = true
  scanProgress.value = 0
  scanErrorMessage.value = ''

  // 重置扫描统计
  scanStats.value = {
    foldersFound: 0,
    filesFound: files.length,
    currentFolder: ''
  }

  try {
    // 开始扫描文件夹结构
    await scanFolderStructure(files)
  } catch (error) {
    console.error('文件夹扫描失败:', error)
    scanErrorMessage.value = error.message || '文件夹扫描失败'
  } finally {
    isScanning.value = false
    scanProgress.value = 100
  }
}

/**
 * 扫描文件夹结构（带进度提示）
 */
const scanFolderStructure = async (files) => {
  console.log('开始扫描文件夹结构...')

  // 模拟扫描进度
  const totalSteps = 5
  let currentStep = 0

  const updateProgress = (step, message) => {
    currentStep = step
    scanProgress.value = Math.round((currentStep / totalSteps) * 100)
    console.log(`扫描进度: ${scanProgress.value}% - ${message}`)
  }

  try {
    // 步骤1：初始化扫描
    updateProgress(1, '初始化文件夹扫描...')
    await new Promise(resolve => setTimeout(resolve, 200))

    // 步骤2：分析文件路径
    updateProgress(2, '分析文件路径结构...')
    const folderStructure = await analyzeFolderStructureWithProgress(files)

    // 步骤3：统计文件夹信息
    updateProgress(3, '统计文件夹信息...')
    scanStats.value.foldersFound = folderStructure.length
    await new Promise(resolve => setTimeout(resolve, 200))

    // 步骤4：验证文件夹结构
    updateProgress(4, '验证文件夹结构...')
    await new Promise(resolve => setTimeout(resolve, 200))

      console.log('解析的文件夹结构:', folderStructure)

    // 步骤5：完成扫描
    updateProgress(5, '扫描完成')

    // 检查是否为多版本文件夹结构
    if (folderStructure.length > 1) {
      // 多版本情况：显示多版本确认界面
      selectedFolderStructure.value = folderStructure
      selectedFiles.value = files

      // 延迟关闭扫描进度，显示多版本弹窗
      setTimeout(() => {
        showScanProgress.value = false
        showMultiVersionModal.value = true
      }, 500)

    } else if (folderStructure.length === 1) {
      // 单版本情况：也使用多版本确认界面（保持一致性）
      selectedFolderStructure.value = folderStructure
      selectedFiles.value = files

      // 延迟关闭扫描进度，显示多版本弹窗
      setTimeout(() => {
        showScanProgress.value = false
        showMultiVersionModal.value = true
      }, 500)

    } else {
      throw new Error('未检测到有效的文件夹结构')
    }

    console.log(`检测到 ${folderStructure.length} 个版本文件夹，总计 ${files.length} 个文件`)

  } catch (error) {
    console.error('扫描文件夹结构失败:', error)
    throw error
  }
}

/**
 * 带进度的文件夹结构分析
 */
const analyzeFolderStructureWithProgress = async (files) => {
  const folderMap = new Map()

  for (let i = 0; i < files.length; i++) {
    const file = files[i]

    // 更新当前处理的文件
    currentScanFile.value = file.name

    // 模拟处理时间（对于大量文件）
    if (i % 50 === 0) {
      await new Promise(resolve => setTimeout(resolve, 10))
    }

    let folderPath = ''
    let folderName = ''

    if (file.webkitRelativePath) {
      const pathParts = file.webkitRelativePath.split('/')
      folderPath = pathParts.slice(0, -1).join('/')
      folderName = pathParts[pathParts.length - 2] // 文件所在的直接父文件夹
    }

    // 创建唯一的文件夹标识
    const folderKey = folderPath

    if (!folderMap.has(folderKey)) {
      folderMap.set(folderKey, {
        folderPath: folderPath,
        folderName: folderName,
        files: [],
        fileCount: 0,
        parseStatus: 'pending',
        parsedMetadata: null
      })

      // 更新文件夹统计
      scanStats.value.foldersFound = folderMap.size
      scanStats.value.currentFolder = folderName
    }

    const folderData = folderMap.get(folderKey)
    folderData.files.push(file)
    folderData.fileCount = folderData.files.length
  }

  // 清空当前文件显示
  currentScanFile.value = ''

  return Array.from(folderMap.values())
}



/**
 * 处理多版本元数据提交
 */
const handleMultiVersionSubmit = async (submitData) => {
  try {
    isCreatingTask.value = true

    console.log('多版本提交数据:', submitData)

    // 计算总文件数
    const totalFileCount = submitData.versions.reduce((total, version) => total + version.fileCount, 0)

    // 构建多版本任务创建DTO
    const multiVersionTaskData = {
      description: `多版本批量上传任务，包含 ${submitData.versions.length} 个版本`,
      deptId: 100, // TODO: 从用户信息获取部门ID
      createdBy: 'admin', // TODO: 从用户信息获取用户名
      totalFileCount: totalFileCount, // 设置总文件数
      versions: submitData.versions.map((version, index) => ({
        folderName: version.folderName,
        folderPath: version.folderPath,
        relativePath: version.folderPath,
        countryId: version.metadata.countryId,
        certTypeId: version.metadata.certTypeId,
        issueYear: version.metadata.issueYear,
        issuePlace: version.metadata.issuePlace,
        certNumberPrefix: version.metadata.certNumberPrefix,
        fileCount: version.fileCount,
        files: version.files.map(file => ({
          fileName: file.name,
          relativePath: file.webkitRelativePath,
          fileSize: file.size,
          fileType: file.type,
          lastModified: file.lastModified
        })),
        versionIndex: index,
        parseStatus: 'SUCCESS'
      }))
    }

    console.log('创建多版本任务:', multiVersionTaskData)
    console.log('总文件数计算:', totalFileCount, '版本详情:', submitData.versions.map(v => ({
      folderName: v.folderName,
      fileCount: v.fileCount,
      filesLength: v.files ? v.files.length : 'undefined'
    })))

    // 使用统一的批量任务创建API（一次性完成）
    const taskData = {
      description: `批量上传任务，包含 ${submitData.versions.length} 个文件夹`,
      deptId: 100, // TODO: 从用户信息获取部门ID
      createdBy: 'admin', // TODO: 从用户信息获取用户名
      folders: submitData.versions.map(version => ({
        folderName: version.folderName,
        folderPath: version.folderPath,
        countryId: version.metadata.countryId,
        certTypeId: version.metadata.certTypeId,
        issueYear: version.metadata.issueYear,
        issuePlace: version.metadata.issuePlace,
        certNumberPrefix: version.metadata.certNumberPrefix,
        fileCount: version.files ? version.files.length : 0
      }))
    }

    console.log('创建批量任务（统一接口）:', taskData)
    const taskResponse = await createBatchTask(taskData)
    const parentTask = taskResponse.data

    console.log('批量任务创建成功（包含所有文件夹）:', parentTask)

    ElMessage.success(`成功创建批量上传任务，包含 ${submitData.versions.length} 个文件夹`)

    // 关闭多版本弹窗
    showMultiVersionModal.value = false

    // 准备 Uppy 上传
    resetUppy()

    // 确保 Uppy 实例存在
    if (!uppy.value) {
      ElMessage.error('上传组件初始化失败，请刷新页面重试')
      return
    }

    // 将所有版本的文件添加到 Uppy，并设置对应的元数据
    submitData.versions.forEach((version, versionIndex) => {
      console.log(`处理版本: ${version.folderName}, 文件数量: ${version.files ? version.files.length : 'undefined'}`)

      if (!version.files || !Array.isArray(version.files)) {
        console.error(`版本 ${version.folderName} 的文件数组无效:`, version.files)
        ElMessage.error(`版本 ${version.folderName} 的文件数据无效`)
        return
      }

      version.files.forEach((file, fileIndex) => {
        // 确保文件名有效
        const fileName = file.name || `unnamed-file-${Date.now()}-${fileIndex}`
        console.log(`添加文件: ${fileName} (原始名称: ${file.name})`)

        // 生成简洁的文件 ID，只使用字母数字和基本符号
        const timestamp = Date.now()
        const randomSuffix = Math.random().toString(36).substr(2, 5)
        const fileId = `file_${versionIndex}_${fileIndex}_${timestamp}_${randomSuffix}`

        console.log(`生成文件ID: ${fileId}`)

        uppy.value.addFile({
          id: fileId, // 使用简洁的文件 ID
          name: fileName, // 确保使用有效的文件名
          type: file.type,
          data: file,
          meta: {
            taskId: parentTask.taskId, // 使用父任务ID
            relativePath: file.webkitRelativePath,
            folderPath: version.folderPath,
            folderName: version.folderName,
            versionIndex: versionIndex,
            certNumberPrefix: version.metadata.certNumberPrefix,
            issuePlace: version.metadata.issuePlace,
            fileName: fileName, // 在元数据中也保存文件名
            originalFileName: fileName
          }
        })
      })
    })

    console.log(`已添加 ${uppy.value.getFiles().length} 个文件到 Uppy`)

    // 打开上传进度弹窗
    showUploadModal.value = true

    // 等待弹窗打开后开始上传
    await nextTick()
    setTimeout(() => {
      if (uppy.value && uppy.value.getFiles().length > 0) {
        console.log('开始多版本批量上传...')
        uppy.value.upload()
      } else {
        ElMessage.warning('没有文件可上传')
      }
    }, 500)

    // 刷新任务列表
    await getTaskList()

  } catch (error) {
    console.error('创建多版本任务失败:', error)
    ElMessage.error('创建多版本任务失败: ' + (error.message || '未知错误'))
  } finally {
    isCreatingTask.value = false
  }
}

/**
 * 处理上传弹窗关闭
 */
const handleUploadModalClose = () => {
  showUploadModal.value = false
  resetUppy()
}

/**
 * 处理上传完成
 */
const handleUploadComplete = (result) => {
  console.log('上传完成:', result)
  ElMessage.success(`上传完成！成功 ${result.successful.length} 个文件`)

  // 刷新任务列表
  getTaskList()
}

/**
 * 处理上传失败
 */
const handleUploadFailed = (result) => {
  console.log('上传失败:', result)
  ElMessage.error(`上传失败！失败 ${result.failed.length} 个文件`)

  // 刷新任务列表
  getTaskList()
}

/**
 * 获取任务列表
 */
const getTaskList = async () => {
  try {
    tableLoading.value = true

    const params = {
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize,
      ...searchForm
    }

    const response = await withErrorHandler(
      () => getBatchTaskList(params),
      { customMessage: '获取任务列表失败，请刷新页面重试' }
    )

    taskList.value = response.rows || []
    pagination.total = response.total || 0

  } catch (error) {
    BatchTaskErrorHandler.handleTaskListError(error)
  } finally {
    tableLoading.value = false
  }
}

/**
 * 刷新任务列表
 */
const refreshTaskList = () => {
  getTaskList()
}

/**
 * 处理搜索
 */
const handleSearch = () => {
  pagination.pageNum = 1
  getTaskList()
}

/**
 * 处理重置搜索
 */
const handleResetSearch = () => {
  searchForm.status = ''
  searchForm.taskName = ''
  pagination.pageNum = 1
  getTaskList()
}

/**
 * 处理分页大小变化
 */
const handleSizeChange = (size) => {
  pagination.pageSize = size
  pagination.pageNum = 1
  getTaskList()
}

/**
 * 处理当前页变化
 */
const handleCurrentChange = (page) => {
  pagination.pageNum = page
  getTaskList()
}







/**
 * 处理删除任务
 */
const handleDeleteTask = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除任务 "${row.taskName}" 吗？此操作不可恢复。`,
      '确认删除',
      { type: 'warning' }
    )

    await withErrorHandler(
      () => deleteBatchTask(row.taskId),
      {
        customMessage: `删除任务 "${row.taskName}" 失败，请稍后重试`,
        showMessage: false // 我们手动处理成功消息
      }
    )

    ElMessage.success('任务删除成功')
    getTaskList()

  } catch (error) {
    if (error !== 'cancel') {
      BatchTaskErrorHandler.handleTaskDeletionError(error)
    }
  }
}

/**
 * 获取状态标签类型
 */
const getStatusTagType = (status) => {
  const statusMap = {
    'UPLOADING': 'warning',
    'COMPLETED': 'success',
    'FAILED': 'danger',
    'CANCELLED': 'info'
  }
  return statusMap[status] || 'info'
}

/**
 * 获取状态文本
 */
const getStatusText = (status) => {
  const statusMap = {
    'UPLOADING': '上传中',
    'COMPLETED': '已完成',
    'FAILED': '失败',
    'CANCELLED': '已取消'
  }
  return statusMap[status] || status
}

/**
 * 获取进度百分比
 */
const getProgressPercentage = (row) => {
  if (row.totalFiles === 0) return 0
  return Math.round((row.processedFiles / row.totalFiles) * 100)
}

/**
 * 获取进度状态
 */
const getProgressStatus = (status) => {
  if (status === 'COMPLETED') return 'success'
  if (status === 'FAILED') return 'exception'
  return null
}

/**
 * 格式化日期时间
 */
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

/**
 * 处理扫描进度关闭
 */
const handleScanProgressClose = () => {
  showScanProgress.value = false

  // 重置扫描状态
  scanProgress.value = 0
  isScanning.value = false
  scanErrorMessage.value = ''
  currentScanFile.value = ''

  // 重置扫描统计
  scanStats.value = {
    foldersFound: 0,
    filesFound: 0,
    currentFolder: ''
  }
}

/**
 * 处理扫描取消
 */
const handleScanCancel = () => {
  console.log('用户取消文件夹扫描')

  // 停止扫描
  isScanning.value = false
  scanErrorMessage.value = '用户取消扫描'

  // 延迟关闭弹窗
  setTimeout(() => {
    handleScanProgressClose()
  }, 1000)
}

/**
 * 处理暂停任务
 */
const handlePauseTask = async (row) => {
  try {
    await withErrorHandler(
      () => pauseBatchTask(row.taskId),
      { customMessage: `暂停任务 "${row.taskName}" 失败，请稍后重试` }
    )

    ElMessage.success('任务已暂停')
    getTaskList()
  } catch (error) {
    BatchTaskErrorHandler.handleApiError(error, {
      customMessage: '暂停任务失败'
    })
  }
}

/**
 * 处理重试任务
 */
const handleRetryTask = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要重试任务 "${row.taskName}" 吗？`,
      '确认重试',
      { type: 'warning' }
    )

    await withErrorHandler(
      () => retryBatchTask(row.taskId),
      { customMessage: `重试任务 "${row.taskName}" 失败，请稍后重试` }
    )

    ElMessage.success('任务重试成功')
    getTaskList()
  } catch (error) {
    if (error !== 'cancel') {
      BatchTaskErrorHandler.handleApiError(error, {
        customMessage: '重试任务失败'
      })
    }
  }
}

/**
 * 处理查看任务详情
 */
const handleViewTask = (row) => {
  // 这里可以跳转到任务详情页面或打开详情弹窗
  console.log('查看任务详情:', row)
  ElMessage.info('任务详情功能开发中...')
}

/**
 * 处理打开任务工作台
 */
const handleOpenWorkspace = (row) => {
  // 跳转到任务工作台页面
  router.push(`/cert/batch/workspace/${row.taskId}`)
}

// 组件挂载时获取任务列表
onMounted(async () => {
  getTaskList()

  // 确保 Uppy 实例已初始化
  if (!uppy.value) {
    console.log('🔧 初始化 Uppy 实例...')
  } else {
    console.log('✅ Uppy 实例已存在')
  }
})
</script>

<style scoped>
.batch-task-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.task-list-section {
  margin-top: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-section {
  margin-bottom: 20px;
  padding: 15px;
  background: #f5f7fa;
  border-radius: 6px;
}

.task-name-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.task-name {
  font-weight: 500;
  color: #303133;
}

.task-id-tag {
  align-self: flex-start;
}

.progress-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.progress-text {
  font-size: 12px;
  color: #909399;
  text-align: center;
}

.pagination-section {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style>
