<template>
  <el-dialog
    v-model="visible"
    title="版本文件夹管理"
    width="1200px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-loading="loading" class="folder-management">
      <!-- 版本基本信息 -->
      <el-card class="version-info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>版本信息</span>
            <el-tag :type="getVersionStatusType(versionInfo.status)">
              {{ getVersionStatusText(versionInfo.status) }}
            </el-tag>
          </div>
        </template>
        
        <el-descriptions :column="4" border>
          <el-descriptions-item label="版本代码">
            <el-text type="primary" size="large">{{ versionInfo.versionCode || '-' }}</el-text>
          </el-descriptions-item>
          <el-descriptions-item label="国家">
            <div class="country-info">
              <span>{{ versionInfo.countryInfo?.name || '-' }}</span>
              <el-text type="info" size="small">{{ versionInfo.countryInfo?.code || '-' }}</el-text>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="证件类型">
            <div class="cert-type-info">
              <span>{{ versionInfo.certInfo?.zjlbmc || '-' }}</span>
              <el-text type="info" size="small">{{ versionInfo.certInfo?.zjlbdm || '-' }}</el-text>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="发行年份">
            {{ versionInfo.issueYear || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="当前标准样本" :span="2">
            <div v-if="versionInfo.standardFolderId" class="standard-folder-info">
              <el-tag type="success" size="large">
                <el-icon><Star /></el-icon>
                {{ getStandardFolderName() }}
              </el-tag>
            </div>
            <el-text v-else type="warning">尚未设置标准样本文件夹</el-text>
          </el-descriptions-item>
          <el-descriptions-item label="关联文件夹数量">
            <el-tag type="info">{{ folderList.length }} 个</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatTime(versionInfo.createTime) }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 标准样本设置说明 -->
      <el-card class="help-card" shadow="never">
        <template #header>
          <span>标准样本设置说明</span>
        </template>
        <el-alert
          title="标准样本文件夹规则"
          type="info"
          :closable="false"
          show-icon
        >
          <template #default>
            <ul class="rule-list">
              <li>每个版本只能设置一个标准样本文件夹</li>
              <li>只有状态为"已关联"且"审核通过"的文件夹才能设为标准样本</li>
              <li>标准样本文件夹中的图片可以进行标注操作</li>
              <li>设置新的标准样本时，原标准样本会自动取消</li>
              <li>取消标准样本后，该版本下所有文件夹都无法进行标注</li>
            </ul>
          </template>
        </el-alert>
      </el-card>

      <!-- 文件夹列表 -->
      <el-card class="folder-list-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>关联文件夹列表</span>
            <div class="header-actions">
              <el-button type="primary" size="small" @click="handleRefresh">
                <el-icon><Refresh /></el-icon>
                刷新列表
              </el-button>
            </div>
          </div>
        </template>

        <el-table
          :data="folderList"
          stripe
          border
          max-height="500"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" align="center" />
          
          <el-table-column label="文件夹名称" min-width="200" show-overflow-tooltip>
            <template #default="scope">
              <div class="folder-name-cell">
                <el-link type="primary" @click="handleViewFolder(scope.row)">
                  {{ scope.row.folderName || scope.row.filename || scope.row.folderId }}
                </el-link>
                <div class="folder-tags">
                  <el-tag 
                    v-if="scope.row.folderId === versionInfo.standardFolderId" 
                    type="success" 
                    size="small"
                    effect="dark"
                  >
                    <el-icon><Star /></el-icon>
                    标准样本
                  </el-tag>
                  <el-tag 
                    v-else 
                    type="info" 
                    size="small"
                    effect="plain"
                  >
                    普通样本
                  </el-tag>
                </div>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column label="文件数量" width="100" align="center">
            <template #default="scope">
              <el-tag type="primary" effect="plain">
                {{ scope.row.imageCount || 0 }} 张
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column label="关联状态" width="120" align="center">
            <template #default="scope">
              <el-tag :type="getFolderStatusType(scope.row.status)">
                {{ getFolderStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column label="审核状态" width="120" align="center">
            <template #default="scope">
              <el-tag :type="getReviewStatusType(scope.row.reviewStatus)">
                {{ getReviewStatusText(scope.row.reviewStatus) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column label="样本类型" width="120" align="center">
            <template #default="scope">
              <div class="sample-type-cell">
                <el-tag 
                  v-if="scope.row.folderId === versionInfo.standardFolderId" 
                  type="success"
                  effect="dark"
                >
                  <el-icon><Star /></el-icon>
                  标准样本
                </el-tag>
                <el-tag 
                  v-else 
                  type="info"
                  effect="plain"
                >
                  普通样本
                </el-tag>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column label="创建时间" width="160" align="center">
            <template #default="scope">
              {{ formatTime(scope.row.createTime) }}
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="220" align="center" fixed="right">
            <template #default="scope">
              <div class="action-buttons">
                <el-button
                  type="primary"
                  size="small"
                  @click="handleViewFolder(scope.row)"
                >
                  查看详情
                </el-button>
                
                <el-button
                  v-if="scope.row.folderId !== versionInfo.standardFolderId && canSetAsStandard(scope.row)"
                  type="success"
                  size="small"
                  @click="handleSetAsStandard(scope.row)"
                >
                  设为标准
                </el-button>
                
                <el-button
                  v-if="scope.row.folderId === versionInfo.standardFolderId"
                  type="warning"
                  size="small"
                  @click="handleRemoveStandard(scope.row)"
                >
                  取消标准
                </el-button>
                
                <el-tooltip
                  v-if="!canSetAsStandard(scope.row) && scope.row.folderId !== versionInfo.standardFolderId"
                  content="只有已关联且审核通过的文件夹才能设为标准样本"
                  placement="top"
                >
                  <el-button
                    type="success"
                    size="small"
                    disabled
                  >
                    设为标准
                  </el-button>
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 空状态 -->
        <div v-if="folderList.length === 0" class="empty-state">
          <el-empty description="暂无关联文件夹">
            <el-button type="primary" @click="handleRefresh">
              刷新数据
            </el-button>
          </el-empty>
        </div>
      </el-card>

      <!-- 批量操作区域 -->
      <el-card v-if="selectedFolders.length > 0" class="batch-actions-card" shadow="never">
        <template #header>
          <span>批量操作</span>
        </template>
        <div class="batch-actions">
          <el-text type="info">已选择 {{ selectedFolders.length }} 个文件夹</el-text>
          <div class="batch-buttons">
            <el-button
              v-if="selectedFolders.length === 1 && canSetAsStandard(selectedFolders[0])"
              type="success"
              @click="handleSetAsStandard(selectedFolders[0])"
            >
              设为标准样本
            </el-button>
            <el-button @click="clearSelection">
              取消选择
            </el-button>
          </div>
        </div>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleRefresh">刷新数据</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Star, Refresh } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'

// 导入API
import { getFoldersByVersionId, setStandardFolder, removeStandardFolder } from '@/api/samples/version'

// 导入工具函数
import {
  isStandardSampleFolder,
  isStandardSampleByVersion,
  canAnnotateImage,
  getFolderTypeDisplayName,
  getFolderTypeTagType
} from '@/utils/folderUtils'

const router = useRouter()

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  versionInfo: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'refresh', 'standard-changed'])

// 响应式数据
const visible = ref(false)
const loading = ref(false)
const folderList = ref([])
const selectedFolders = ref([])

// 监听显示状态
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal && props.versionInfo?.versionId) {
    loadFolderList()
  }
})

watch(() => visible.value, (newVal) => {
  emit('update:modelValue', newVal)
  if (!newVal) {
    clearSelection()
  }
})

// 计算属性
const canSetAsStandard = computed(() => {
  return (folder) => {
    return folder.status === 'associated' && folder.reviewStatus === 'approved'
  }
})

// 方法
const loadFolderList = async () => {
  if (!props.versionInfo?.versionId) return
  
  loading.value = true
  try {
    const response = await getFoldersByVersionId(props.versionInfo.versionId)
    if (response.code === 200) {
      folderList.value = response.data || []
    } else {
      ElMessage.error(response.msg || '加载文件夹列表失败')
    }
  } catch (error) {
    console.error('加载文件夹列表失败:', error)
    ElMessage.error('加载文件夹列表失败')
  } finally {
    loading.value = false
  }
}

const handleClose = () => {
  visible.value = false
}

const handleRefresh = () => {
  loadFolderList()
  emit('refresh')
}

const handleSelectionChange = (selection) => {
  selectedFolders.value = selection
}

const clearSelection = () => {
  selectedFolders.value = []
}

const handleViewFolder = (folder) => {
  router.push({
    path: `/cert/folder/${folder.folderId}`
  })
}

const handleSetAsStandard = async (folder) => {
  if (!canSetAsStandard.value(folder)) {
    ElMessage.warning('只有已关联且审核通过的文件夹才能设为标准样本')
    return
  }
  
  try {
    const currentStandardName = getStandardFolderName()
    let confirmMessage = `确定要将文件夹"${folder.folderName || folder.filename || folder.folderId}"设为标准样本吗？`
    
    if (props.versionInfo.standardFolderId) {
      confirmMessage += `\n\n当前标准样本"${currentStandardName}"将被自动取消。`
    }
    
    await ElMessageBox.confirm(
      confirmMessage,
      '设置标准样本',
      {
        confirmButtonText: '确定设置',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: false
      }
    )
    
    loading.value = true
    const response = await setStandardFolder(props.versionInfo.versionId, {
      folderId: folder.folderId
    })
    
    if (response.code === 200) {
      ElMessage.success('设置标准样本成功')
      
      // 更新本地数据
      props.versionInfo.standardFolderId = folder.folderId
      
      // 通知父组件
      emit('standard-changed', {
        versionId: props.versionInfo.versionId,
        standardFolderId: folder.folderId,
        folderName: folder.folderName || folder.filename
      })
      
      // 刷新数据
      await loadFolderList()
      emit('refresh')
    } else {
      ElMessage.error(response.msg || '设置标准样本失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('设置标准样本失败:', error)
      ElMessage.error('设置标准样本失败')
    }
  } finally {
    loading.value = false
  }
}

const handleRemoveStandard = async (folder) => {
  try {
    await ElMessageBox.confirm(
      `确定要取消文件夹"${folder.folderName || folder.filename || folder.folderId}"的标准样本设置吗？\n\n取消后，该版本下所有文件夹都无法进行标注操作。`,
      '取消标准样本',
      {
        confirmButtonText: '确定取消',
        cancelButtonText: '保持现状',
        type: 'warning',
        dangerouslyUseHTMLString: false
      }
    )
    
    loading.value = true
    const response = await removeStandardFolder(props.versionInfo.versionId)
    
    if (response.code === 200) {
      ElMessage.success('取消标准样本成功')
      
      // 更新本地数据
      props.versionInfo.standardFolderId = null
      
      // 通知父组件
      emit('standard-changed', {
        versionId: props.versionInfo.versionId,
        standardFolderId: null,
        folderName: null
      })
      
      // 刷新数据
      await loadFolderList()
      emit('refresh')
    } else {
      ElMessage.error(response.msg || '取消标准样本失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消标准样本失败:', error)
      ElMessage.error('取消标准样本失败')
    }
  } finally {
    loading.value = false
  }
}

const getStandardFolderName = () => {
  if (!props.versionInfo.standardFolderId) return ''
  
  const standardFolder = folderList.value.find(
    folder => folder.folderId === props.versionInfo.standardFolderId
  )
  
  return standardFolder?.folderName || standardFolder?.filename || standardFolder?.folderId || '未知文件夹'
}

// 工具方法
const getVersionStatusType = (status) => {
  const statusMap = {
    'active': 'success',
    'inactive': 'info',
    'pending': 'warning',
    'disabled': 'danger'
  }
  return statusMap[status] || 'info'
}

const getVersionStatusText = (status) => {
  const statusMap = {
    'active': '启用',
    'inactive': '停用',
    'pending': '待审核',
    'disabled': '禁用'
  }
  return statusMap[status] || '未知'
}

const getFolderStatusType = (status) => {
  const statusMap = {
    'associated': 'success',
    'unassociated': 'warning',
    'processing': 'info'
  }
  return statusMap[status] || 'info'
}

const getFolderStatusText = (status) => {
  const statusMap = {
    'associated': '已关联',
    'unassociated': '未关联',
    'processing': '处理中'
  }
  return statusMap[status] || '未知'
}

const getReviewStatusType = (status) => {
  const statusMap = {
    'approved': 'success',
    'pending': 'warning',
    'rejected': 'danger'
  }
  return statusMap[status] || 'info'
}

const getReviewStatusText = (status) => {
  const statusMap = {
    'approved': '已通过',
    'pending': '待审核',
    'rejected': '已驳回'
  }
  return statusMap[status] || '未审核'
}

const formatTime = (time) => {
  if (!time) return '-'
  return new Date(time).toLocaleString('zh-CN')
}
</script>

<style scoped>
.folder-management {
  max-height: 80vh;
  overflow-y: auto;
}

.version-info-card,
.help-card,
.folder-list-card,
.batch-actions-card {
  margin-bottom: 20px;
}

.batch-actions-card {
  margin-bottom: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.country-info,
.cert-type-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.standard-folder-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.rule-list {
  margin: 0;
  padding-left: 20px;
}

.rule-list li {
  margin: 4px 0;
  line-height: 1.5;
}

.folder-name-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.folder-tags {
  display: flex;
  gap: 4px;
}

.sample-type-cell {
  display: flex;
  justify-content: center;
}

.action-buttons {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
  justify-content: center;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
}

.batch-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.batch-buttons {
  display: flex;
  gap: 8px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
