<template>
  <el-dialog
    v-model="visible"
    title="版本详情"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-loading="loading" class="version-detail">
      <!-- 基本信息 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>基本信息</span>
            <el-button type="primary" size="small" @click="handleEdit">
              编辑版本
            </el-button>
          </div>
        </template>
        
        <el-descriptions :column="2" border>
          <el-descriptions-item label="版本代码">
            <el-text type="primary" size="large">{{ versionInfo.versionCode || '-' }}</el-text>
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(versionInfo.status)">
              {{ getStatusText(versionInfo.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="国家">
            <div class="country-info">
              <span>{{ versionInfo.countryInfo?.name || '-' }}</span>
              <el-text type="info" size="small">{{ versionInfo.countryInfo?.code || '-' }}</el-text>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="证件类型">
            <div class="cert-type-info">
              <span>{{ versionInfo.certInfo?.zjlbmc || '-' }}</span>
              <el-text type="info" size="small">{{ versionInfo.certInfo?.zjlbdm || '-' }}</el-text>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="发行年份">
            {{ versionInfo.issueYear || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatTime(versionInfo.createTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="创建者">
            <div class="creator-info">
              <span>{{ versionInfo.creatorInfo?.nickName || versionInfo.creatorInfo?.userName || '-' }}</span>
              <el-text type="info" size="small">{{ versionInfo.deptInfo?.deptName || '-' }}</el-text>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="更新时间">
            {{ formatTime(versionInfo.updateTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="描述" :span="2">
            <el-text>{{ versionInfo.description || '暂无描述' }}</el-text>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 标准样本信息 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>标准样本设置</span>
            <div class="header-actions">
              <el-button 
                v-if="!versionInfo.standardFolderId" 
                type="success" 
                size="small" 
                @click="handleSetStandardFolder"
              >
                设置标准样本
              </el-button>
              <el-button 
                v-else 
                type="warning" 
                size="small" 
                @click="handleChangeStandardFolder"
              >
                更换标准样本
              </el-button>
              <el-button 
                v-if="versionInfo.standardFolderId" 
                type="danger" 
                size="small" 
                @click="handleRemoveStandardFolder"
              >
                取消设置
              </el-button>
            </div>
          </div>
        </template>

        <div v-if="versionInfo.standardFolderId" class="standard-folder-info">
          <el-alert
            title="已设置标准样本文件夹"
            type="success"
            :closable="false"
            show-icon
          >
            <template #default>
              <div class="standard-info">
                <p><strong>文件夹ID:</strong> {{ versionInfo.standardFolderId }}</p>
                <p><strong>设置时间:</strong> {{ formatTime(versionInfo.updateTime) }}</p>
                <el-button type="primary" size="small" @click="handleViewStandardFolder">
                  查看文件夹详情
                </el-button>
              </div>
            </template>
          </el-alert>
        </div>
        <div v-else class="no-standard-folder">
          <el-empty description="尚未设置标准样本文件夹">
            <el-button type="primary" @click="handleSetStandardFolder">
              立即设置
            </el-button>
          </el-empty>
        </div>
      </el-card>

      <!-- 训练图片信息 -->
      <el-card v-if="versionInfo.trainingImage" class="info-card" shadow="never">
        <template #header>
          <span>训练图片</span>
        </template>
        
        <div class="training-image-info">
          <div class="image-preview">
            <el-image
              :src="versionInfo.trainingImage.url"
              :preview-src-list="[versionInfo.trainingImage.url]"
              fit="cover"
              style="width: 200px; height: 120px;"
            >
              <template #error>
                <div class="image-slot">
                  <el-icon><Picture /></el-icon>
                  <span>图片加载失败</span>
                </div>
              </template>
            </el-image>
          </div>
          <div class="image-description">
            <p><strong>描述:</strong> {{ versionInfo.trainingImage.description || '暂无描述' }}</p>
            <p><strong>图片URL:</strong> {{ versionInfo.trainingImage.url || '-' }}</p>
          </div>
        </div>
      </el-card>

      <!-- 关联文件夹列表 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>关联文件夹 ({{ folderList.length }})</span>
            <el-button type="primary" size="small" @click="handleManageFolders">
              管理文件夹
            </el-button>
          </div>
        </template>

        <el-table :data="folderList" stripe border max-height="300">
          <el-table-column label="文件夹名称" prop="folderName" show-overflow-tooltip />
          <el-table-column label="状态" prop="status" width="100" align="center">
            <template #default="scope">
              <el-tag :type="getFolderStatusType(scope.row.status)">
                {{ getFolderStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="类型" width="100" align="center">
            <template #default="scope">
              <el-tag v-if="scope.row.folderId === versionInfo.standardFolderId" type="success">
                标准样本
              </el-tag>
              <el-tag v-else type="info">
                普通样本
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="图片数量" prop="imageCount" width="100" align="center">
            <template #default="scope">
              {{ scope.row.imageCount || 0 }} 张
            </template>
          </el-table-column>
          <el-table-column label="创建时间" prop="createTime" width="160" align="center">
            <template #default="scope">
              {{ formatTime(scope.row.createTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" align="center">
            <template #default="scope">
              <el-button type="primary" size="small" @click="handleViewFolder(scope.row)">
                查看详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <div v-if="folderList.length === 0" class="empty-folders">
          <el-empty description="暂无关联文件夹" />
        </div>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleRefresh">刷新数据</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Picture } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'

// 导入API
import { getVersionDetails, getFoldersByVersionId } from '@/api/samples/version'

const router = useRouter()

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  versionInfo: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'refresh'])

// 响应式数据
const visible = ref(false)
const loading = ref(false)
const folderList = ref([])

// 计算属性
const versionDetail = computed(() => props.versionInfo)

// 监听显示状态
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal && props.versionInfo?.versionId) {
    loadVersionDetail()
    loadFolderList()
  }
})

watch(() => visible.value, (newVal) => {
  emit('update:modelValue', newVal)
})

// 方法
const loadVersionDetail = async () => {
  if (!props.versionInfo?.versionId) return
  
  loading.value = true
  try {
    const response = await getVersionDetails(props.versionInfo.versionId)
    if (response.code === 200) {
      // 更新版本信息
      Object.assign(props.versionInfo, response.data)
    }
  } catch (error) {
    console.error('加载版本详情失败:', error)
    ElMessage.error('加载版本详情失败')
  } finally {
    loading.value = false
  }
}

const loadFolderList = async () => {
  if (!props.versionInfo?.versionId) return
  
  try {
    const response = await getFoldersByVersionId(props.versionInfo.versionId)
    if (response.code === 200) {
      folderList.value = response.data || []
    }
  } catch (error) {
    console.error('加载文件夹列表失败:', error)
    ElMessage.error('加载文件夹列表失败')
  }
}

const handleClose = () => {
  visible.value = false
}

const handleRefresh = () => {
  loadVersionDetail()
  loadFolderList()
  emit('refresh')
}

const handleEdit = () => {
  ElMessage.info('编辑版本功能开发中...')
}

const handleSetStandardFolder = () => {
  ElMessage.info('设置标准样本功能开发中...')
}

const handleChangeStandardFolder = () => {
  ElMessage.info('更换标准样本功能开发中...')
}

const handleRemoveStandardFolder = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要取消设置标准样本文件夹吗？',
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    ElMessage.info('取消标准样本功能开发中...')
  } catch (error) {
    // 用户取消操作
  }
}

const handleViewStandardFolder = () => {
  if (props.versionInfo.standardFolderId) {
    router.push({
      path: `/cert/folder/${props.versionInfo.standardFolderId}`
    })
  }
}

const handleManageFolders = () => {
  emit('update:modelValue', false)
  // 触发父组件的文件夹管理
  emit('manage-folders', props.versionInfo)
}

const handleViewFolder = (folder) => {
  router.push({
    path: `/cert/folder/${folder.folderId}`
  })
}

// 工具方法
const getStatusType = (status) => {
  const statusMap = {
    'active': 'success',
    'inactive': 'info',
    'pending': 'warning',
    'disabled': 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    'active': '启用',
    'inactive': '停用',
    'pending': '待审核',
    'disabled': '禁用'
  }
  return statusMap[status] || '未知'
}

const getFolderStatusType = (status) => {
  const statusMap = {
    'associated': 'success',
    'unassociated': 'warning',
    'processing': 'info'
  }
  return statusMap[status] || 'info'
}

const getFolderStatusText = (status) => {
  const statusMap = {
    'associated': '已关联',
    'unassociated': '未关联',
    'processing': '处理中'
  }
  return statusMap[status] || '未知'
}

const formatTime = (time) => {
  if (!time) return '-'
  return new Date(time).toLocaleString('zh-CN')
}
</script>

<style scoped>
.version-detail {
  max-height: 70vh;
  overflow-y: auto;
}

.info-card {
  margin-bottom: 20px;
}

.info-card:last-child {
  margin-bottom: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.country-info,
.cert-type-info,
.creator-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.standard-folder-info {
  margin-bottom: 16px;
}

.standard-info {
  margin-top: 8px;
}

.standard-info p {
  margin: 4px 0;
}

.no-standard-folder {
  text-align: center;
  padding: 20px;
}

.training-image-info {
  display: flex;
  gap: 20px;
  align-items: flex-start;
}

.image-preview {
  flex-shrink: 0;
}

.image-slot {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #909399;
}

.image-description {
  flex: 1;
}

.image-description p {
  margin: 8px 0;
  word-break: break-all;
}

.empty-folders {
  text-align: center;
  padding: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
