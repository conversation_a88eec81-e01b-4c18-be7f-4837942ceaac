/**
 * 统一的文件夹管理 Composable
 * 提供文件夹信息管理、状态处理等功能
 */

import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { getFolderDetails, getFolderList } from '@/api/samples/folder'
import type { FolderInfoVO, FolderStatus, FolderQueryParams } from '@/types'

export function useFolderInfo() {
  // 响应式数据
  const folderInfo = ref<FolderInfoVO | null>(null)
  const folderList = ref<FolderInfoVO[]>([])
  const loading = ref(false)
  const listLoading = ref(false)

  // 计算属性
  const hasFolderInfo = computed(() => !!folderInfo.value)
  const folderStatus = computed(() => folderInfo.value?.status || 'unknown')
  const isStandardSample = computed(() => folderInfo.value?.isStandardSample || false)
  const imageCount = computed(() => folderInfo.value?.imageCount || 0)

  // 方法
  const loadFolderInfo = async (folderId: string) => {
    if (!folderId) {
      throw new Error('文件夹ID不能为空')
    }

    loading.value = true
    try {
      const response = await getFolderDetails(folderId)
      if (response.code === 200) {
        folderInfo.value = response.data
        return folderInfo.value
      } else {
        throw new Error(response.msg || '获取文件夹详情失败')
      }
    } catch (error) {
      console.error('加载文件夹信息失败:', error)
      ElMessage.error('加载文件夹信息失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  const loadFolderList = async (params: FolderQueryParams = {}) => {
    listLoading.value = true
    try {
      const response = await getFolderList(params)
      if (response.code === 200) {
        folderList.value = response.rows || []
        return folderList.value
      } else {
        throw new Error(response.msg || '获取文件夹列表失败')
      }
    } catch (error) {
      console.error('加载文件夹列表失败:', error)
      ElMessage.error('加载文件夹列表失败')
      throw error
    } finally {
      listLoading.value = false
    }
  }

  const getStatusType = (status: FolderStatus | string) => {
    const statusMap: Record<string, string> = {
      'unassociated': 'warning',
      'associated': 'success',
      'PROCESSING': 'primary',
      'COMPLETED': 'success',
      'FAILED': 'danger',
      '待处理': 'warning',
      '处理中': 'primary',
      '已完成': 'success',
      '已取消': 'danger'
    }
    return statusMap[status] || 'info'
  }

  const getStatusText = (status: FolderStatus | string) => {
    const statusMap: Record<string, string> = {
      'unassociated': '未关联',
      'associated': '已关联',
      'PROCESSING': '处理中',
      'COMPLETED': '已完成',
      'FAILED': '失败',
      '待处理': '待处理',
      '处理中': '处理中',
      '已完成': '已完成',
      '已取消': '已取消'
    }
    return statusMap[status] || '未知'
  }

  const reset = () => {
    folderInfo.value = null
    folderList.value = []
    loading.value = false
    listLoading.value = false
  }

  return {
    // 响应式数据
    folderInfo,
    folderList,
    loading,
    listLoading,
    
    // 计算属性
    hasFolderInfo,
    folderStatus,
    isStandardSample,
    imageCount,
    
    // 方法
    loadFolderInfo,
    loadFolderList,
    getStatusType,
    getStatusText,
    reset
  }
}

/**
 * 文件夹选择管理
 */
export function useFolderSelection() {
  const selectedFolders = ref<string[]>([])
  const maxSelection = ref<number | null>(null)

  const selectFolder = (folderId: string) => {
    if (maxSelection.value && selectedFolders.value.length >= maxSelection.value) {
      ElMessage.warning(`最多只能选择 ${maxSelection.value} 个文件夹`)
      return false
    }

    if (!selectedFolders.value.includes(folderId)) {
      selectedFolders.value.push(folderId)
      return true
    }
    return false
  }

  const unselectFolder = (folderId: string) => {
    const index = selectedFolders.value.indexOf(folderId)
    if (index > -1) {
      selectedFolders.value.splice(index, 1)
      return true
    }
    return false
  }

  const toggleFolder = (folderId: string) => {
    if (selectedFolders.value.includes(folderId)) {
      return unselectFolder(folderId)
    } else {
      return selectFolder(folderId)
    }
  }

  const isSelected = (folderId: string) => {
    return selectedFolders.value.includes(folderId)
  }

  const clearSelection = () => {
    selectedFolders.value = []
  }

  const setMaxSelection = (max: number | null) => {
    maxSelection.value = max
  }

  return {
    selectedFolders,
    maxSelection,
    selectFolder,
    unselectFolder,
    toggleFolder,
    isSelected,
    clearSelection,
    setMaxSelection
  }
}

/**
 * 文件夹过滤和搜索
 */
export function useFolderFilter() {
  const searchKeyword = ref('')
  const statusFilter = ref<FolderStatus | ''>('')
  const countryFilter = ref('')
  const certTypeFilter = ref('')
  const yearFilter = ref('')

  const filterFolders = (folders: FolderInfoVO[]) => {
    return folders.filter(folder => {
      // 关键词搜索
      if (searchKeyword.value) {
        const keyword = searchKeyword.value.toLowerCase()
        const matchName = folder.folderName.toLowerCase().includes(keyword)
        const matchPath = folder.folderPath.toLowerCase().includes(keyword)
        if (!matchName && !matchPath) return false
      }

      // 状态过滤
      if (statusFilter.value && folder.status !== statusFilter.value) {
        return false
      }

      // 国家过滤
      if (countryFilter.value && folder.countryInfo?.countryCode !== countryFilter.value) {
        return false
      }

      // 证件类型过滤
      if (certTypeFilter.value && folder.certTypeInfo?.certType !== certTypeFilter.value) {
        return false
      }

      // 年份过滤
      if (yearFilter.value && folder.issueYear !== yearFilter.value) {
        return false
      }

      return true
    })
  }

  const resetFilters = () => {
    searchKeyword.value = ''
    statusFilter.value = ''
    countryFilter.value = ''
    certTypeFilter.value = ''
    yearFilter.value = ''
  }

  return {
    searchKeyword,
    statusFilter,
    countryFilter,
    certTypeFilter,
    yearFilter,
    filterFolders,
    resetFilters
  }
} 