import { defineStore } from 'pinia'
import { 
  getVersionTemplates,
  getAnnotationTemplate,
  saveAnnotationTemplate,
  deleteAnnotationTemplate,
  deleteVersionTemplates,
  copyTemplatesToVersion,
  getTemplateStatistics,
  validateAnnotationTemplate,
  type VersionAnnotationTemplate,
  type AnnotationTemplateDTO,
  type TemplateStatistics
} from '@/api/samples/annotation'
import { ElMessage } from 'element-plus'

interface AnnotationTemplateState {
  /** 模板缓存 Map<versionId, Map<imageType, template>> */
  templates: Map<string, Map<string, VersionAnnotationTemplate>>
  /** 版本统计信息缓存 Map<versionId, statistics> */
  statistics: Map<string, TemplateStatistics>
  /** 加载状态 */
  loading: boolean
  /** 当前操作的版本ID */
  currentVersionId: string | null
  /** 当前选中的模板 */
  currentTemplate: VersionAnnotationTemplate | null
}

export const useAnnotationTemplateStore = defineStore('annotationTemplate', {
  state: (): AnnotationTemplateState => ({
    templates: new Map(),
    statistics: new Map(),
    loading: false,
    currentVersionId: null,
    currentTemplate: null
  }),

  getters: {
    /**
     * 获取指定版本的所有模板
     */
    getVersionTemplates: (state) => (versionId: string) => {
      return state.templates.get(versionId) || new Map()
    },

    /**
     * 获取指定版本和图片类型的模板
     */
    getTemplate: (state) => (versionId: string, imageType: string) => {
      return state.templates.get(versionId)?.get(imageType)
    },

    /**
     * 获取版本的模板列表（数组形式）
     */
    getVersionTemplateList: (state) => (versionId: string) => {
      const versionTemplates = state.templates.get(versionId)
      return versionTemplates ? Array.from(versionTemplates.values()) : []
    },

    /**
     * 获取版本统计信息
     */
    getVersionStatistics: (state) => (versionId: string) => {
      return state.statistics.get(versionId)
    },

    /**
     * 检查版本是否有指定类型的模板
     */
    hasTemplate: (state) => (versionId: string, imageType: string) => {
      return state.templates.get(versionId)?.has(imageType) ?? false
    },

    /**
     * 获取版本的模板数量
     */
    getTemplateCount: (state) => (versionId: string) => {
      return state.templates.get(versionId)?.size ?? 0
    },

    /**
     * 获取所有已缓存的版本ID列表
     */
    getCachedVersionIds: (state) => {
      return Array.from(state.templates.keys())
    }
  },

  actions: {
    /**
     * 加载版本的所有模板
     */
    async loadVersionTemplates(versionId: string, force = false) {
      // 如果已缓存且不强制刷新，直接返回
      if (!force && this.templates.has(versionId)) {
        return this.getVersionTemplateList(versionId)
      }

      this.loading = true
      try {
        const response = await getVersionTemplates(versionId)
        
        if (response.code === 200) {
          const templates = new Map<string, VersionAnnotationTemplate>()
          
          // 将数组转换为Map，以imageType为key
          response.rows.forEach(template => {
            templates.set(template.imageType, template)
          })
          
          this.templates.set(versionId, templates)
          return Array.from(templates.values())
        } else {
          ElMessage.error(response.msg || '加载模板失败')
          return []
        }
      } catch (error: any) {
        ElMessage.error(error.message || '网络请求失败')
        return []
      } finally {
        this.loading = false
      }
    },

    /**
     * 加载指定模板
     */
    async loadTemplate(versionId: string, imageType: string, force = false) {
      // 如果已缓存且不强制刷新，直接返回
      if (!force && this.hasTemplate(versionId, imageType)) {
        return this.getTemplate(versionId, imageType)
      }

      this.loading = true
      try {
        const response = await getAnnotationTemplate(versionId, imageType)
        
        if (response.code === 200) {
          // 确保版本Map存在
          if (!this.templates.has(versionId)) {
            this.templates.set(versionId, new Map())
          }
          
          // 缓存模板
          this.templates.get(versionId)!.set(imageType, response.data)
          return response.data
        } else {
          ElMessage.error(response.msg || '加载模板失败')
          return null
        }
      } catch (error: any) {
        ElMessage.error(error.message || '网络请求失败')
        return null
      } finally {
        this.loading = false
      }
    },

    /**
     * 保存模板
     */
    async saveTemplate(templateData: AnnotationTemplateDTO) {
      this.loading = true
      try {
        const response = await saveAnnotationTemplate(templateData)
        
        if (response.code === 200) {
          // 更新缓存
          if (!this.templates.has(templateData.versionId)) {
            this.templates.set(templateData.versionId, new Map())
          }
          
          this.templates.get(templateData.versionId)!.set(templateData.imageType, response.data)
          this.currentTemplate = response.data
          
          // 清除统计缓存，强制重新加载
          this.statistics.delete(templateData.versionId)
          
          ElMessage.success('保存模板成功')
          return response.data
        } else {
          ElMessage.error(response.msg || '保存模板失败')
          return null
        }
      } catch (error: any) {
        ElMessage.error(error.message || '网络请求失败')
        return null
      } finally {
        this.loading = false
      }
    },

    /**
     * 删除模板
     */
    async deleteTemplate(templateId: string, versionId: string, imageType: string) {
      this.loading = true
      try {
        const response = await deleteAnnotationTemplate(templateId)
        
        if (response.code === 200) {
          // 从缓存中移除
          this.templates.get(versionId)?.delete(imageType)
          
          // 清除统计缓存
          this.statistics.delete(versionId)
          
          // 如果是当前模板，清除选中状态
          if (this.currentTemplate?.templateId === templateId) {
            this.currentTemplate = null
          }
          
          ElMessage.success('删除模板成功')
          return true
        } else {
          ElMessage.error(response.msg || '删除模板失败')
          return false
        }
      } catch (error: any) {
        ElMessage.error(error.message || '网络请求失败')
        return false
      } finally {
        this.loading = false
      }
    },

    /**
     * 删除版本的所有模板
     */
    async deleteVersionTemplates(versionId: string) {
      this.loading = true
      try {
        const response = await deleteVersionTemplates(versionId)
        
        if (response.code === 200) {
          // 清除缓存
          this.templates.delete(versionId)
          this.statistics.delete(versionId)
          
          // 如果当前版本被删除，清除相关状态
          if (this.currentVersionId === versionId) {
            this.currentVersionId = null
            this.currentTemplate = null
          }
          
          ElMessage.success('删除版本模板成功')
          return true
        } else {
          ElMessage.error(response.msg || '删除版本模板失败')
          return false
        }
      } catch (error: any) {
        ElMessage.error(error.message || '网络请求失败')
        return false
      } finally {
        this.loading = false
      }
    },

    /**
     * 复制模板到新版本
     */
    async copyTemplates(sourceVersionId: string, targetVersionId: string, newStandardFolderId: string) {
      this.loading = true
      try {
        const response = await copyTemplatesToVersion(sourceVersionId, targetVersionId, newStandardFolderId)
        
        if (response.code === 200) {
          // 更新目标版本的缓存
          const targetTemplates = new Map<string, VersionAnnotationTemplate>()
          response.data.forEach(template => {
            targetTemplates.set(template.imageType, template)
          })
          
          this.templates.set(targetVersionId, targetTemplates)
          
          // 清除目标版本的统计缓存
          this.statistics.delete(targetVersionId)
          
          ElMessage.success(`成功复制${response.data.length}个模板`)
          return response.data
        } else {
          ElMessage.error(response.msg || '复制模板失败')
          return []
        }
      } catch (error: any) {
        ElMessage.error(error.message || '网络请求失败')
        return []
      } finally {
        this.loading = false
      }
    },

    /**
     * 加载版本统计信息
     */
    async loadStatistics(versionId: string, force = false) {
      // 如果已缓存且不强制刷新，直接返回
      if (!force && this.statistics.has(versionId)) {
        return this.statistics.get(versionId)!
      }

      this.loading = true
      try {
        const response = await getTemplateStatistics(versionId)
        
        if (response.code === 200) {
          this.statistics.set(versionId, response.data)
          return response.data
        } else {
          ElMessage.error(response.msg || '加载统计信息失败')
          return null
        }
      } catch (error: any) {
        ElMessage.error(error.message || '网络请求失败')
        return null
      } finally {
        this.loading = false
      }
    },

    /**
     * 验证模板数据
     */
    async validateTemplate(templateData: AnnotationTemplateDTO) {
      try {
        const response = await validateAnnotationTemplate(templateData)
        return response.code === 200
      } catch (error: any) {
        ElMessage.error(error.message || '验证失败')
        return false
      }
    },

    /**
     * 设置当前版本
     */
    setCurrentVersion(versionId: string) {
      this.currentVersionId = versionId
    },

    /**
     * 设置当前模板
     */
    setCurrentTemplate(template: VersionAnnotationTemplate | null) {
      this.currentTemplate = template
    },

    /**
     * 清除指定版本的缓存
     */
    clearVersionCache(versionId: string) {
      this.templates.delete(versionId)
      this.statistics.delete(versionId)
    },

    /**
     * 清除所有缓存
     */
    clearAllCache() {
      this.templates.clear()
      this.statistics.clear()
      this.currentVersionId = null
      this.currentTemplate = null
    }
  }
})
