/**
 * 批量任务管理 Composable
 * 提供批量上传、任务管理等功能
 */

import { ref, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import Uppy from '@uppy/core'
import Tus from '@uppy/tus'
import { getToken } from '@/utils/auth'
import type { UploadProgress, BatchTaskCreateDTO } from '@/types'

export function useBatchTasks() {
  const uppy = ref<Uppy | null>(null)
  const isUploading = ref(false)
  const uploadProgress = ref(0)
  const currentTask = ref<BatchTaskCreateDTO | null>(null)

  // 上传状态对象（兼容旧版本）
  const uploadState = ref<UploadProgress>({
    isUploading: false,
    progress: 0,
    currentFile: '',
    uploadSpeed: '',      // 改为空字符串
    remainingTime: '',    // 改为空字符串
    uploadedFiles: 0,
    totalFiles: 0,
    failedFiles: 0
  })

  // 创建完整的 Uppy 实例（包含 Tus 插件）
  const createUppy = () => {
    if (uppy.value) {
      try {
        uppy.value.destroy()
      } catch (error) {
        console.warn('销毁现有实例时出错:', error)
      }
    }

    // 清理 Tus 相关的本地存储
    try {
      Object.keys(localStorage).forEach(key => {
        if (key.includes('tus') || key.includes('uppy')) {
          localStorage.removeItem(key)
          console.log('🧹 清理本地存储:', key)
        }
      })
    } catch (e) {
      console.warn('清理本地存储失败:', e)
    }

    const token = getToken()
    console.log('🔐 当前认证令牌:', token ? `存在 (${token.length} 字符)` : '不存在')

    uppy.value = new Uppy({
      debug: true,
      autoProceed: false,
      allowMultipleUploads: false,
      restrictions: {
        maxFileSize: 50 * 1024 * 1024, // 50MB
        allowedFileTypes: ['.jpg', '.jpeg', '.png', '.pdf', '.JPG', '.JPEG', '.PNG', '.PDF']
      },
      meta: {
        disableThumbnailGenerator: true
      }
    })

    // 添加 Tus 插件
    const tusEndpoint = '/dev-api/tus/uploads'
    console.log('🔧 正在配置 Tus 插件，端点:', tusEndpoint)

    uppy.value.use(Tus, {
      endpoint: tusEndpoint,
      headers: token ? {
        'Authorization': `Bearer ${token}`
      } : {},
      chunkSize: 2 * 1024 * 1024,
      retryDelays: [0, 1000, 3000, 5000, 10000, 15000, 30000],
      limit: 1,
      removeFingerprintOnSuccess: true,
      storeFingerprintForResuming: false,
      debug: true,
      onError: (error) => {
        console.error('🚨 Tus 插件错误:', error)
      },
      onProgress: (bytesUploaded, bytesTotal) => {
        console.log(`📊 上传进度: ${bytesUploaded}/${bytesTotal} (${Math.round(bytesUploaded/bytesTotal*100)}%)`)
      }
    })

    // 添加事件监听器
    uppy.value.on('upload', () => {
      console.log('🚀 Uppy 开始上传')
      isUploading.value = true
      uploadState.value.isUploading = true
    })

    uppy.value.on('complete', (result) => {
      console.log('✅ Uppy 上传完成:', result)
      isUploading.value = false
      uploadState.value.isUploading = false
    })

    uppy.value.on('error', (error) => {
      console.error('❌ Uppy 错误:', error)
      ElMessage.error('上传过程中发生错误')
    })

    uppy.value.on('upload-error', (file, error) => {
      console.error('❌ 文件上传失败:', file?.name, error)

      let errorMessage = `文件 ${file?.name || '未知'} 上传失败`
      if (error?.message?.includes('ERR_CONNECTION_RESET')) {
        errorMessage += ': 网络连接被重置，请检查网络连接或稍后重试'
      } else if (error?.message) {
        errorMessage += `: ${error.message}`
      }

      ElMessage.error(errorMessage)
    })

    // 关键修复：使用 onBeforeFileAdded 提前拦截和修复
    uppy.value.on('file-added', (file) => {
      // 在文件被添加时立即检查和修复
      console.log(`[file-added] 检查文件: ${file.id}`)
      console.log(`[file-added] 文件名: ${file.name} (类型: ${typeof file.name})`)

      // 检查文件名是否有效
      if (!file.name || typeof file.name !== 'string') {
        console.warn(`[file-added] 检测到无效文件名，正在修复。ID: ${file.id}, 原始文件名: ${file.name}`)

        // 尝试从各种可能的来源获取文件名
        const newName = (file.meta?.name as string) ||
                        (file.meta?.fileName as string) ||
                        (file.data?.name as string) ||
                        `unnamed-file-${Date.now()}-${Math.random().toString(36).substr(2, 5)}`

        console.log(`[file-added] 准备修复文件名为: "${newName}"`)

        try {
          // 使用 setFileState 更新文件名（关键修复）
          uppy.value?.setFileState(file.id, { name: newName })
          console.log(`[file-added] ✅ 文件名已修复为: "${newName}"`)
        } catch (e) {
          console.error(`[file-added] ❌ 修复文件名失败:`, e)
          // 如果修复失败，移除这个有问题的文件
          try {
            uppy.value?.removeFile(file.id)
            console.log(`[file-added] 已移除有问题的文件: ${file.id}`)
          } catch (removeError) {
            console.error(`[file-added] 移除文件也失败了:`, removeError)
          }
          return
        }
      }

      console.log('📁 文件已添加 (经过检查):', file.name, file.size, 'bytes')
      console.log('📁 文件元数据:', file.meta)

      if (file.meta) {
        const { taskId, folderPath, folderName, relativePath, certNumberPrefix, issuePlace } = file.meta

        const enhancedMeta = {
          taskId: taskId || '',
          folderPath: folderPath || '',
          folderName: folderName || '',
          relativePath: relativePath || file.webkitRelativePath || '',
          certNumberPrefix: certNumberPrefix || '',
          issuePlace: issuePlace || '',
          fileName: file.name,
          originalFileName: file.name
        }

        try {
          uppy.value?.setFileState(file.id, { meta: enhancedMeta })
          console.log('📁 增强后的元数据:', enhancedMeta)
        } catch (metaError) {
          console.error('设置元数据失败:', metaError)
        }
      }
    })

    uppy.value.on('upload-progress', (file, progress) => {
      console.log(`📊 文件 ${file.name} 上传进度:`, progress)
      uploadProgress.value = progress.bytesUploaded / progress.bytesTotal * 100
      uploadState.value.progress = uploadProgress.value
      uploadState.value.currentFile = file.name
    })

    return uppy.value
  }

  // 初始化时创建实例（关键修复）
  createUppy()

  const resetUppy = () => {
    if (uppy.value) {
      try {
        uppy.value.cancelAll()
        uppy.value.getFiles().forEach(file => {
          uppy.value.removeFile(file.id)
        })
      } catch (error) {
        console.warn('重置文件时出错:', error)
      }
    }

    isUploading.value = false
    uploadProgress.value = 0
    uploadState.value = {
      isUploading: false,
      progress: 0,
      currentFile: '',
      uploadSpeed: '',     // 改为空字符串
      remainingTime: '',   // 改为空字符串
      uploadedFiles: 0,
      totalFiles: 0,
      failedFiles: 0
    }

    // 重新创建 Uppy 实例（不设置为 null）
    createUppy()
    console.log('✅ Uppy 实例已重置并重新创建')
  }

  const installUppy = (taskId: string, certNumberPrefix: string, issuePlace: string) => {
    console.log('🔧 安装 Uppy 实例:', { taskId, certNumberPrefix, issuePlace })

    if (!uppy.value) {
      createUppy()
    }

    if (uppy.value) {
      // 设置全局元数据
      uppy.value.setMeta({
        taskId,
        certNumberPrefix,
        issuePlace
      })

      console.log('✅ Uppy 实例配置完成')
      return true
    }

    return false
  }

  const setMultiLevelMetadata = (metadata: Record<string, any>) => {
    if (!uppy.value) {
      console.warn('Uppy 实例未初始化')
      return false
    }

    console.log('📝 设置多级文件夹元数据:', metadata)

    // 更新全局元数据
    uppy.value.setMeta(metadata)

    // 更新现有文件的元数据
    const files = uppy.value.getFiles()
    files.forEach(file => {
      const currentMeta = file.meta || {}
      const enhancedMeta = { ...currentMeta, ...metadata }
      uppy.value?.setFileState(file.id, { meta: enhancedMeta })
    })

    console.log('✅ 多级文件夹元数据设置完成')
    return true
  }

  const validateFileMetadata = (file: any) => {
    const requiredFields = ['taskId', 'folderPath', 'folderName']
    const missingFields = requiredFields.filter(field => !file.meta?.[field])

    if (missingFields.length > 0) {
      console.warn(`⚠️ 文件 ${file.name} 缺少必要元数据:`, missingFields)
      return false
    }

    console.log(`✅ 文件 ${file.name} 元数据验证通过`)
    return true
  }

  const testConnection = async () => {
    console.log('🔍 开始连接测试...')

    try {
      const response = await fetch('/dev-api/tus/uploads', {
        method: 'OPTIONS',
        headers: {
          'Authorization': `Bearer ${getToken()}`,
          'Tus-Resumable': '1.0.0'
        }
      })

      console.log('🔍 连接测试响应:', response.status, response.statusText)

      if (response.ok) {
        console.log('✅ 连接测试成功')
        return true
      } else {
        console.error('❌ 连接测试失败:', response.status, response.statusText)
        return false
      }
    } catch (error) {
      console.error('❌ 连接测试异常:', error)
      return false
    }
  }

  const runNetworkDiagnostics = async () => {
    console.log('🔍 开始网络诊断...')

    const diagnostics = {
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      online: navigator.onLine,
      connection: (navigator as any).connection?.effectiveType || 'unknown',
      tests: {} as Record<string, boolean>
    }

    // 测试基本连接
    diagnostics.tests.basicConnection = await testConnection()

    // 测试认证
    const token = getToken()
    diagnostics.tests.hasToken = !!token
    diagnostics.tests.tokenLength = token ? token.length : 0

    // 测试本地存储
    try {
      localStorage.setItem('test', 'test')
      localStorage.removeItem('test')
      diagnostics.tests.localStorage = true
    } catch (e) {
      diagnostics.tests.localStorage = false
    }

    console.log('🔍 网络诊断结果:', diagnostics)
    return diagnostics
  }

  const uploadWithFallback = async (files: File[], metadata: Record<string, any>) => {
    console.log('🚀 开始上传（带回退机制）:', files.length, '个文件')

    // 首先运行网络诊断
    const diagnostics = await runNetworkDiagnostics()

    if (!diagnostics.tests.basicConnection) {
      throw new Error('网络连接不可用，请检查网络设置')
    }

    if (!diagnostics.tests.hasToken) {
      throw new Error('认证令牌无效，请重新登录')
    }

    // 创建 Uppy 实例
    if (!uppy.value) {
      createUppy()
    }

    if (!uppy.value) {
      throw new Error('无法初始化上传组件')
    }

    // 设置元数据
    setMultiLevelMetadata(metadata)

    // 添加文件
    files.forEach(file => {
      uppy.value?.addFile({
        name: file.name,
        type: file.type,
        data: file,
        source: 'local',
        isRemote: false
      })
    })

    // 开始上传
    try {
      const result = await uppy.value.upload()
      console.log('✅ 上传完成:', result)
      return result
    } catch (error) {
      console.error('❌ 上传失败:', error)
      throw error
    }
  }

  // 组件卸载时销毁 Uppy 实例（简化处理）
  onUnmounted(() => {
    if (uppy.value) {
      try {
        uppy.value.destroy()
        console.log('🧹 Uppy 实例已在组件卸载时销毁')
      } catch (error) {
        console.warn('销毁 Uppy 实例时出错:', error)
      }
    }
  })

  return {
    uppy,
    isUploading,
    uploadProgress,
    currentTask,
    uploadState,

    createUppy,
    resetUppy,
    installUppy,
    setMultiLevelMetadata,
    validateFileMetadata,
    testConnection,
    runNetworkDiagnostics,
    uploadWithFallback
  }
}
